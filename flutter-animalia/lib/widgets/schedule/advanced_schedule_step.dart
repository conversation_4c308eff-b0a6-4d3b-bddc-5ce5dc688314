import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../../models/staff_working_hours_settings.dart';
import '../../models/working_hours_settings.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/role_provider.dart';
import '../../services/staff_working_hours_service.dart';
import '../../utils/debug_logger.dart';

/// Cutting-edge schedule configuration step with excellent UX
/// - Fetches and merges all staff schedules to set as default
/// - Allows editing and adding time intervals
/// - Refined visuals with strong hierarchy
class AdvancedScheduleStep extends StatefulWidget {
  final Function(Map<String, DaySchedule> mergedSchedule)? onScheduleChanged;
  final VoidCallback? onNext;
  final VoidCallback? onBack;
  final String? title;
  final String? subtitle;
  final Map<String, Map<String, String>>? initialBusinessHours; // For WebsiteManagementScreen
  final Function(Map<String, Map<String, String>>)? onChanged; // For WebsiteManagementScreen

  const AdvancedScheduleStep({
    Key? key,
    this.onScheduleChanged,
    this.onNext,
    this.onBack,
    this.title,
    this.subtitle,
    this.initialBusinessHours, this.onChanged,
  }) : super(key: key);

  @override
  State<AdvancedScheduleStep> createState() => _AdvancedScheduleStepState();
}

class _AdvancedScheduleStepState extends State<AdvancedScheduleStep>
    with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  Map<String, DaySchedule> _mergedSchedule = {};
  Map<String, List<TimeInterval>> _intervals = {};
  String? _error;
  Map<String, Map<String, String>>? _originalBusinessHours; // Store original hours for reference

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<String> _weekDays = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday'
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadAndMergeSchedules();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.05),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Load all staff schedules and intelligently merge them
  Future<void> _loadAndMergeSchedules() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // First check if we have initial business hours from the website management screen
      if (widget.initialBusinessHours != null && widget.initialBusinessHours!.isNotEmpty) {
        DebugLogger.logVerbose('📅 Using provided initial business hours');

        // Store original business hours for reference
        _originalBusinessHours = Map.from(widget.initialBusinessHours!);

        final convertedSchedule = _convertBusinessHoursToSchedule(widget.initialBusinessHours!);

        setState(() {
          _mergedSchedule = convertedSchedule;
          _intervals = _convertScheduleToIntervals(convertedSchedule);
          _isLoading = false;
        });

        DebugLogger.logSuccess('✅ Successfully loaded initial business hours');

        // Notify parent with the converted schedule
        _notifyScheduleChanged();
        return;
      }

      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

      // Get all staff members from calendar provider
      final staffMembers = calendarProvider.availableStaff;

      if (staffMembers.isEmpty) {
        DebugLogger.logWarning('No staff members found, using default schedule');
        _setDefaultSchedule();
        return;
      }

      DebugLogger.logVerbose('📅 Loading schedules for ${staffMembers.length} staff members');

      // Fetch all staff schedules
      final staffIds = staffMembers.map((s) => s.id).toList();
      final response = await StaffWorkingHoursService.getBatchStaffWorkingHours(staffIds);

      if (!response.success || response.data == null) {
        DebugLogger.logWarning('Failed to load staff schedules: ${response.error}');
        _setDefaultSchedule();
        return;
      }

      // Merge schedules intelligently
      final mergedSchedule = _mergeStaffSchedules(response.data!.staffWorkingHours);

      setState(() {
        _mergedSchedule = mergedSchedule;
        _intervals = _convertScheduleToIntervals(mergedSchedule);
        _isLoading = false;
      });

      DebugLogger.logSuccess('✅ Successfully loaded and merged schedules');

      // Notify parent
      _notifyScheduleChanged();

    } catch (e) {
      DebugLogger.logError('❌ Error loading schedules: $e');
      setState(() {
        _error = 'Failed to load schedules: $e';
        _isLoading = false;
      });
      _setDefaultSchedule();
    }
  }

  /// Intelligently merge multiple staff schedules
  /// - Takes the earliest start time across all staff
  /// - Takes the latest end time across all staff
  /// - Marks day as working if any staff works that day
  /// - Merges break times intelligently
  Map<String, DaySchedule> _mergeStaffSchedules(List<StaffWorkingHoursSettings> staffSettings) {
    final merged = <String, DaySchedule>{};

    for (final day in _weekDays) {
      String? earliestStart;
      String? latestEnd;
      bool anyoneWorks = false;
      String? mergedBreakStart;
      String? mergedBreakEnd;

      for (final staff in staffSettings) {
        final daySchedule = staff.weeklySchedule[day];
        if (daySchedule == null || !daySchedule.isWorkingDay) continue;

        anyoneWorks = true;

        // Track earliest start time
        if (daySchedule.startTime != null) {
          if (earliestStart == null || _compareTime(daySchedule.startTime!, earliestStart) < 0) {
            earliestStart = daySchedule.startTime;
          }
        }

        // Track latest end time
        if (daySchedule.endTime != null) {
          if (latestEnd == null || _compareTime(daySchedule.endTime!, latestEnd) > 0) {
            latestEnd = daySchedule.endTime;
          }
        }

        // Track break times (use first found for simplicity)
        if (daySchedule.breakStart != null && mergedBreakStart == null) {
          mergedBreakStart = daySchedule.breakStart;
        }
        if (daySchedule.breakEnd != null && mergedBreakEnd == null) {
          mergedBreakEnd = daySchedule.breakEnd;
        }
      }

      merged[day] = DaySchedule(
        isWorkingDay: anyoneWorks,
        startTime: earliestStart ?? '09:00',
        endTime: latestEnd ?? '17:00',
        breakStart: mergedBreakStart,
        breakEnd: mergedBreakEnd,
      );
    }

    return merged;
  }

  /// Compare two time strings (HH:mm format)
  int _compareTime(String time1, String time2) {
    final parts1 = time1.split(':');
    final parts2 = time2.split(':');
    final minutes1 = int.parse(parts1[0]) * 60 + int.parse(parts1[1]);
    final minutes2 = int.parse(parts2[0]) * 60 + int.parse(parts2[1]);
    return minutes1.compareTo(minutes2);
  }

  /// Convert schedule to interval format for advanced editing
  Map<String, List<TimeInterval>> _convertScheduleToIntervals(Map<String, DaySchedule> schedule) {
    final intervals = <String, List<TimeInterval>>{};

    for (final entry in schedule.entries) {
      final day = entry.key;
      final daySchedule = entry.value;

      if (!daySchedule.isWorkingDay || daySchedule.startTime == null || daySchedule.endTime == null) {
        intervals[day] = [];
        continue;
      }

      // If there's a break, split into two intervals
      if (daySchedule.breakStart != null && daySchedule.breakEnd != null) {
        intervals[day] = [
          TimeInterval(start: daySchedule.startTime!, end: daySchedule.breakStart!),
          TimeInterval(start: daySchedule.breakEnd!, end: daySchedule.endTime!),
        ];
      } else {
        intervals[day] = [
          TimeInterval(start: daySchedule.startTime!, end: daySchedule.endTime!),
        ];
      }
    }

    return intervals;
  }

  /// Convert business hours format to DaySchedule format
  Map<String, DaySchedule> _convertBusinessHoursToSchedule(Map<String, Map<String, String>> businessHours) {
    final schedule = <String, DaySchedule>{};

    for (final day in _weekDays) {
      final dayData = businessHours[day];
      if (dayData != null) {
        final isOpen = dayData['isOpen'] == 'true';
        final openTime = dayData['open'] ?? '09:00';
        final closeTime = dayData['close'] ?? '17:00';

        schedule[day] = DaySchedule(
          isWorkingDay: isOpen,
          startTime: isOpen ? openTime : null,
          endTime: isOpen ? closeTime : null,
        );
      } else {
        // Fallback for missing day data
        schedule[day] = const DaySchedule(isWorkingDay: false);
      }
    }

    return schedule;
  }

  /// Convert DaySchedule format back to business hours format
  Map<String, Map<String, String>> _convertScheduleToBusinessHours(Map<String, DaySchedule> schedule) {
    final businessHours = <String, Map<String, String>>{};

    for (final day in _weekDays) {
      final daySchedule = schedule[day];
      if (daySchedule != null) {
        // Use original times if available, otherwise use current schedule times or defaults
        String openTime;
        String closeTime;

        if (_originalBusinessHours != null && _originalBusinessHours![day] != null) {
          // Preserve original times for closed days, use current times for open days
          if (daySchedule.isWorkingDay) {
            openTime = daySchedule.startTime ?? _originalBusinessHours![day]!['open'] ?? '09:00';
            closeTime = daySchedule.endTime ?? _originalBusinessHours![day]!['close'] ?? '17:00';
          } else {
            // For closed days, preserve the original times
            openTime = _originalBusinessHours![day]!['open'] ?? '09:00';
            closeTime = _originalBusinessHours![day]!['close'] ?? '17:00';
          }
        } else {
          // No original data, use current schedule or defaults
          openTime = daySchedule.startTime ?? '09:00';
          closeTime = daySchedule.endTime ?? '17:00';
        }

        businessHours[day] = {
          'isOpen': daySchedule.isWorkingDay.toString(),
          'open': openTime,
          'close': closeTime,
        };
      } else {
        // Fallback for missing day data
        businessHours[day] = {
          'isOpen': 'false',
          'open': '09:00',
          'close': '17:00',
        };
      }
    }

    return businessHours;
  }

  /// Notify both callbacks when schedule changes
  void _notifyScheduleChanged() {
    // Notify the standard callback (for onboarding and other screens)
    widget.onScheduleChanged?.call(_mergedSchedule);

    // Notify the website management callback if available
    if (widget.onChanged != null) {
      final businessHours = _convertScheduleToBusinessHours(_mergedSchedule);
      widget.onChanged?.call(businessHours);
    }
  }

  /// Set default schedule (Mon-Fri 9-17, Sat 9-14, Sun closed)
  void _setDefaultSchedule() {
    final defaultSchedule = <String, DaySchedule>{};

    for (final day in _weekDays) {
      if (day == 'sunday') {
        defaultSchedule[day] = const DaySchedule(isWorkingDay: false);
      } else if (day == 'saturday') {
        defaultSchedule[day] = const DaySchedule(
          isWorkingDay: true,
          startTime: '09:00',
          endTime: '14:00',
        );
      } else {
        defaultSchedule[day] = const DaySchedule(
          isWorkingDay: true,
          startTime: '09:00',
          endTime: '17:00',
        );
      }
    }

    setState(() {
      _mergedSchedule = defaultSchedule;
      _intervals = _convertScheduleToIntervals(defaultSchedule);
      _isLoading = false;
    });

    _notifyScheduleChanged();
  }

  Map<String, String> _getDayNames(BuildContext context) {
    return {
      'monday': context.tr('schedule_management.weekdays.monday'),
      'tuesday': context.tr('schedule_management.weekdays.tuesday'),
      'wednesday': context.tr('schedule_management.weekdays.wednesday'),
      'thursday': context.tr('schedule_management.weekdays.thursday'),
      'friday': context.tr('schedule_management.weekdays.friday'),
      'saturday': context.tr('schedule_management.weekdays.saturday'),
      'sunday': context.tr('schedule_management.weekdays.sunday'),
    };
  }

  @override
  Widget build(BuildContext context) {
    // If used as a standalone widget without navigation (e.g., in WebsiteManagementScreen)
    // Just show the schedule editor
    if (widget.onNext == null && widget.onBack == null) {
      if (_isLoading) {
        return _buildLoadingState();
      }
      if (_error != null) {
        return _buildErrorState();
      }
      return _buildScheduleEditor();
    }

    // Full widget with header and navigation
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          children: [
            // Header
            _buildHeader(),
            const SizedBox(height: 32),

            // Content
            Expanded(
              child: _isLoading
                  ? _buildLoadingState()
                  : _error != null
                      ? _buildErrorState()
                      : _buildScheduleEditor(),
            ),

            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.schedule,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title ?? context.tr('schedule_step.configure_schedule'),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.subtitle ?? context.tr('schedule_step.merged_from_staff'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              strokeWidth: 4,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            context.tr('schedule_step.loading_schedules'),
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              context.tr('schedule_step.error_loading'),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error ?? 'Unknown error',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadAndMergeSchedules,
              icon: const Icon(Icons.refresh),
              label: Text(context.tr('schedule_step.retry')),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleEditor() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day schedule cards - more compact
          ..._weekDays.map((day) => _buildDayCard(day)),

          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildDayCard(String day) {
    final dayNames = _getDayNames(context);
    final dayName = dayNames[day] ?? day;
    final schedule = _mergedSchedule[day];
    final intervals = _intervals[day] ?? [];
    final isWorking = schedule?.isWorkingDay ?? false;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        elevation: isWorking ? 2 : 0,
        borderRadius: BorderRadius.circular(12),
        color: isWorking
            ? Theme.of(context).colorScheme.surface
            : Theme.of(context).colorScheme.surfaceContainerHighest,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: isWorking ? null : () => _toggleDay(day, true),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isWorking
                    ? Theme.of(context).primaryColor.withValues(alpha: 0.3)
                    : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: isWorking ? 1.5 : 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Day header with toggle - more compact
                  Row(
                    children: [
                      // Day indicator dot
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: isWorking
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          dayName,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isWorking
                                ? Theme.of(context).colorScheme.onSurface
                                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                          ),
                        ),
                      ),
                      // Compact switch
                      Transform.scale(
                        scale: 0.85,
                        child: Switch(
                          value: isWorking,
                          onChanged: (value) => _toggleDay(day, value),
                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                    ],
                  ),

                  // Intervals - more compact
                  if (isWorking) ...[
                    const SizedBox(height: 12),
                    ...intervals.asMap().entries.map((entry) {
                      final index = entry.key;
                      final interval = entry.value;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: _buildIntervalRow(day, index, interval),
                      );
                    }),

                    // Add interval button - more subtle
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: InkWell(
                        onTap: () => _addInterval(day),
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withValues(alpha: 0.08),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.add,
                                size: 16,
                                color: Theme.of(context).primaryColor,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                context.tr('schedule_step.add_interval'),
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ] else ...[
                    const SizedBox(height: 4),
                    Padding(
                      padding: const EdgeInsets.only(left: 20),
                      child: Text(
                        context.tr('schedule_step.closed'),
                        style: TextStyle(
                          fontSize: 13,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIntervalRow(String day, int index, TimeInterval interval) {
    return Row(
      children: [
        // Left margin to align with day name
        const SizedBox(width: 20),
        Expanded(
          child: InkWell(
            onTap: () => _editInterval(day, index, interval),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 16,
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.7),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${interval.start} - ${interval.end}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).primaryColor,
                          letterSpacing: 0.2,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    Icons.edit_outlined,
                    size: 16,
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
                  ),
                ],
              ),
            ),
          ),
        ),
        // Remove button only if multiple intervals
        if ((_intervals[day]?.length ?? 0) > 1) ...[
          const SizedBox(width: 8),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _removeInterval(day, index),
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.all(6),
                child: Icon(
                  Icons.close,
                  size: 18,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          if (widget.onBack != null)
            Expanded(
              child: OutlinedButton(
                onPressed: widget.onBack,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  context.tr('schedule_step.back'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          if (widget.onBack != null) const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: widget.onNext,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    context.tr('schedule_step.continue'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Icon(Icons.arrow_forward, size: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Quick action methods
  void _applyStandardHours() {
    final standardSchedule = <String, DaySchedule>{};
    for (final day in _weekDays) {
      if (day == 'sunday') {
        standardSchedule[day] = const DaySchedule(isWorkingDay: false);
      } else if (day == 'saturday') {
        standardSchedule[day] = const DaySchedule(
          isWorkingDay: true,
          startTime: '09:00',
          endTime: '14:00',
        );
      } else {
        standardSchedule[day] = const DaySchedule(
          isWorkingDay: true,
          startTime: '09:00',
          endTime: '17:00',
        );
      }
    }

    setState(() {
      _mergedSchedule = standardSchedule;
      _intervals = _convertScheduleToIntervals(standardSchedule);
    });

    _notifyScheduleChanged();
  }

  void _applyExtendedHours() {
    final extendedSchedule = <String, DaySchedule>{};
    for (final day in _weekDays) {
      if (day == 'sunday') {
        extendedSchedule[day] = const DaySchedule(isWorkingDay: false);
      } else {
        extendedSchedule[day] = const DaySchedule(
          isWorkingDay: true,
          startTime: '08:00',
          endTime: '20:00',
        );
      }
    }

    setState(() {
      _mergedSchedule = extendedSchedule;
      _intervals = _convertScheduleToIntervals(extendedSchedule);
    });

    _notifyScheduleChanged();
  }

  void _applyAlwaysOpen() {
    final alwaysOpenSchedule = <String, DaySchedule>{};
    for (final day in _weekDays) {
      alwaysOpenSchedule[day] = const DaySchedule(
        isWorkingDay: true,
        startTime: '00:00',
        endTime: '23:59',
      );
    }

    setState(() {
      _mergedSchedule = alwaysOpenSchedule;
      _intervals = _convertScheduleToIntervals(alwaysOpenSchedule);
    });

    _notifyScheduleChanged();
  }

  // Schedule editing methods
  void _toggleDay(String day, bool isWorking) {
    setState(() {
      if (isWorking) {
        _mergedSchedule[day] = const DaySchedule(
          isWorkingDay: true,
          startTime: '09:00',
          endTime: '17:00',
        );
        _intervals[day] = [TimeInterval(start: '09:00', end: '17:00')];
      } else {
        _mergedSchedule[day] = const DaySchedule(isWorkingDay: false);
        _intervals[day] = [];
      }
    });

    _notifyScheduleChanged();
  }

  void _addInterval(String day) {
    final currentIntervals = _intervals[day] ?? [];
    final newInterval = TimeInterval(start: '09:00', end: '17:00');

    setState(() {
      _intervals[day] = [...currentIntervals, newInterval];
      _updateScheduleFromIntervals(day);
    });

    _notifyScheduleChanged();
  }

  void _removeInterval(String day, int index) {
    final currentIntervals = List<TimeInterval>.from(_intervals[day] ?? []);
    currentIntervals.removeAt(index);

    setState(() {
      _intervals[day] = currentIntervals;
      if (currentIntervals.isEmpty) {
        _mergedSchedule[day] = const DaySchedule(isWorkingDay: false);
      } else {
        _updateScheduleFromIntervals(day);
      }
    });

    _notifyScheduleChanged();
  }

  Future<void> _editInterval(String day, int index, TimeInterval interval) async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _IntervalEditDialog(
        interval: interval,
        dayName: _getDayNames(context)[day] ?? day,
      ),
    );

    if (result != null) {
      final currentIntervals = List<TimeInterval>.from(_intervals[day] ?? []);
      currentIntervals[index] = TimeInterval(
        start: result['start']!,
        end: result['end']!,
      );

      setState(() {
        _intervals[day] = currentIntervals;
        _updateScheduleFromIntervals(day);
      });

      _notifyScheduleChanged();
    }
  }

  void _updateScheduleFromIntervals(String day) {
    final intervals = _intervals[day] ?? [];
    if (intervals.isEmpty) {
      _mergedSchedule[day] = const DaySchedule(isWorkingDay: false);
      return;
    }

    if (intervals.length == 1) {
      _mergedSchedule[day] = DaySchedule(
        isWorkingDay: true,
        startTime: intervals[0].start,
        endTime: intervals[0].end,
      );
    } else if (intervals.length >= 2) {
      // If multiple intervals, treat gap as break
      _mergedSchedule[day] = DaySchedule(
        isWorkingDay: true,
        startTime: intervals[0].start,
        endTime: intervals[intervals.length - 1].end,
        breakStart: intervals[0].end,
        breakEnd: intervals[1].start,
      );
    }
  }
}

/// Model for time intervals
class TimeInterval {
  final String start;
  final String end;

  TimeInterval({required this.start, required this.end});
}

/// Dialog for editing time intervals
class _IntervalEditDialog extends StatefulWidget {
  final TimeInterval interval;
  final String dayName;

  const _IntervalEditDialog({
    required this.interval,
    required this.dayName,
  });

  @override
  State<_IntervalEditDialog> createState() => _IntervalEditDialogState();
}

class _IntervalEditDialogState extends State<_IntervalEditDialog> {
  late String _startTime;
  late String _endTime;

  @override
  void initState() {
    super.initState();
    _startTime = widget.interval.start;
    _endTime = widget.interval.end;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      title: Row(
        children: [
          Icon(Icons.schedule, color: Theme.of(context).primaryColor),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              context.tr('schedule_step.edit_interval'),
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.dayName,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 24),

            // Start time
            _buildTimeSelector(
              label: context.tr('schedule_step.start_time'),
              time: _startTime,
              onTap: () async {
                final time = await _selectTime(_startTime);
                if (time != null) {
                  setState(() => _startTime = time);
                }
              },
            ),

            const SizedBox(height: 16),

            // End time
            _buildTimeSelector(
              label: context.tr('schedule_step.end_time'),
              time: _endTime,
              onTap: () async {
                final time = await _selectTime(_endTime);
                if (time != null) {
                  setState(() => _endTime = time);
                }
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(context.tr('schedule_step.cancel')),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop({
              'start': _startTime,
              'end': _endTime,
            });
          },
          child: Text(context.tr('schedule_step.save')),
        ),
      ],
    );
  }

  Widget _buildTimeSelector({
    required String label,
    required String time,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                Icon(
                  Icons.access_time,
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<String?> _selectTime(String currentTime) async {
    final parts = currentTime.split(':');
    final initialHour = int.parse(parts[0]);
    final initialMinute = int.parse(parts[1]);

    final result = await showModalBottomSheet<Map<String, int>>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return _CupertinoTimePickerBottomSheet(
          title: context.tr('schedule_step.select_time'),
          initialHour: initialHour,
          initialMinute: initialMinute,
        );
      },
    );

    if (result != null) {
      return '${result['hour'].toString().padLeft(2, '0')}:${result['minute'].toString().padLeft(2, '0')}';
    }
    return null;
  }
}

/// iOS-style time picker bottom sheet with haptic feedback
class _CupertinoTimePickerBottomSheet extends StatefulWidget {
  final String title;
  final int initialHour;
  final int initialMinute;

  const _CupertinoTimePickerBottomSheet({
    required this.title,
    required this.initialHour,
    required this.initialMinute,
  });

  @override
  State<_CupertinoTimePickerBottomSheet> createState() => _CupertinoTimePickerBottomSheetState();
}

class _CupertinoTimePickerBottomSheetState extends State<_CupertinoTimePickerBottomSheet> {
  late int _selectedHour;
  late int _selectedMinute;

  @override
  void initState() {
    super.initState();
    _selectedHour = widget.initialHour;
    _selectedMinute = widget.initialMinute;
  }

  void _triggerSelectionHaptic() {
    HapticFeedback.selectionClick();
  }

  void _triggerConfirmationHaptic() {
    HapticFeedback.mediumImpact();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      height: 380,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with title and buttons
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: colorScheme.outlineVariant.withOpacity(0.5),
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Cancel button
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: colorScheme.primary,
                      fontSize: 16,
                    ),
                  ),
                ),

                // Title
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),

                // Done button
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    _triggerConfirmationHaptic();
                    Navigator.of(context).pop({
                      'hour': _selectedHour,
                      'minute': _selectedMinute,
                    });
                  },
                  child: Text(
                    'Done',
                    style: TextStyle(
                      color: colorScheme.primary,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Time picker wheels
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  // Hour picker
                  Expanded(
                    flex: 2,
                    child: CupertinoPicker(
                      itemExtent: 44,
                      diameterRatio: 1.07,
                      squeeze: 1.25,
                      useMagnifier: true,
                      magnification: 1.22,
                      scrollController: FixedExtentScrollController(
                        initialItem: _selectedHour,
                      ),
                      onSelectedItemChanged: (index) {
                        _triggerSelectionHaptic();
                        setState(() {
                          _selectedHour = index;
                        });
                      },
                      children: List<Widget>.generate(24, (index) {
                        return Center(
                          child: Text(
                            index.toString().padLeft(2, '0'),
                            style: TextStyle(
                              fontSize: 22,
                              color: colorScheme.onSurface,
                            ),
                          ),
                        );
                      }),
                    ),
                  ),

                  // Separator
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      ':',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),

                  // Minute picker (15-minute intervals)
                  Expanded(
                    flex: 2,
                    child: CupertinoPicker(
                      itemExtent: 44,
                      diameterRatio: 1.07,
                      squeeze: 1.25,
                      useMagnifier: true,
                      magnification: 1.22,
                      scrollController: FixedExtentScrollController(
                        initialItem: _selectedMinute ~/ 15,
                      ),
                      onSelectedItemChanged: (index) {
                        _triggerSelectionHaptic();
                        setState(() {
                          _selectedMinute = index * 15;
                        });
                      },
                      children: List<Widget>.generate(4, (index) {
                        final minute = index * 15;
                        return Center(
                          child: Text(
                            minute.toString().padLeft(2, '0'),
                            style: TextStyle(
                              fontSize: 22,
                              color: colorScheme.onSurface,
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

