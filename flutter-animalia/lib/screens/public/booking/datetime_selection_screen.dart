import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../models/online_booking.dart';
import '../../../models/salon_web_preferences.dart';
import '../../../services/online_booking_service.dart';
import '../../../utils/debug_logger.dart';

/// Screen for selecting date and time for online booking
class DateTimeSelectionScreen extends StatefulWidget {
  final BookingFlowState bookingState;
  final SalonWebPreferences salonPreferences;

  const DateTimeSelectionScreen({
    super.key,
    required this.bookingState,
    required this.salonPreferences,
  });

  @override
  State<DateTimeSelectionScreen> createState() => _DateTimeSelectionScreenState();
}

class _DateTimeSelectionScreenState extends State<DateTimeSelectionScreen> {
  DateTime _currentWeekStart = DateTime.now();
  DateTime? _selectedDate;
  TimeSlot? _selectedTimeSlot;
  List<TimeSlot> _availableSlots = [];
  bool _isLoadingSlots = false;
  String? _errorMessage;

  // Cache for availability by date
  final Map<String, List<TimeSlot>> _availabilityCache = {};
  final Map<String, bool> _loadingDates = {};
  final Set<String> _daysWithAvailability = {};
  bool _isLoadingWeek = false;
  DateTime? _nextAvailableDay;

  @override
  void initState() {
    super.initState();
    // Set current week start to Monday of this week
    final now = DateTime.now();
    _currentWeekStart = _getWeekStart(now);
    // Pre-select today's date
    _selectedDate = now;
    _loadAvailableSlots();
    // Pre-load availability for the current week
    _preloadWeekAvailability(_currentWeekStart);
  }

  DateTime _getWeekStart(DateTime date) {
    // Get Monday of the week containing this date
    final weekday = date.weekday; // 1 = Monday, 7 = Sunday
    return DateTime(date.year, date.month, date.day).subtract(Duration(days: weekday - 1));
  }

  Color get _primaryColor => _parseColor(widget.salonPreferences.primaryColor);

  Color _parseColor(String hexColor) {
    try {
      final hex = hexColor.replaceAll('#', '');
      return Color(int.parse('FF$hex', radix: 16));
    } catch (e) {
      return Colors.blue[700]!;
    }
  }

  Future<void> _preloadWeekAvailability(DateTime weekStart) async {
    setState(() {
      _isLoadingWeek = true;
      _nextAvailableDay = null;
    });

    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);

    // Load availability for 7 days of the week
    for (int i = 0; i < 7; i++) {
      final date = weekStart.add(Duration(days: i));

      // Skip past days
      if (date.isBefore(todayStart)) {
        continue;
      }

      final dateStr = DateFormat('yyyy-MM-dd').format(date);

      // Skip if already loading or loaded
      if (_loadingDates[dateStr] == true || _availabilityCache.containsKey(dateStr)) {
        continue;
      }

      _loadingDates[dateStr] = true;

      // Load in background without blocking UI
      _loadAvailabilitySlotsForDate(date).then((_) {
        _loadingDates[dateStr] = false;
      });
    }

    // Find next available day (search up to 60 days ahead)
    _findNextAvailableDay();

    setState(() {
      _isLoadingWeek = false;
    });
  }

  Future<void> _findNextAvailableDay() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);

    for (int i = 0; i < 60; i++) {
      final date = todayStart.add(Duration(days: i));
      final dateStr = DateFormat('yyyy-MM-dd').format(date);

      // Check if we already have data
      if (_availabilityCache.containsKey(dateStr)) {
        if (_availabilityCache[dateStr]!.isNotEmpty) {
          setState(() {
            _nextAvailableDay = date;
          });
          return;
        }
      } else {
        // Load this day's availability
        await _loadAvailabilitySlotsForDate(date);
        if (_availabilityCache[dateStr]?.isNotEmpty == true) {
          setState(() {
            _nextAvailableDay = date;
          });
          return;
        }
      }
    }
  }

  Future<void> _loadAvailabilitySlotsForDate(DateTime date) async {
    final dateStr = DateFormat('yyyy-MM-dd').format(date);

    try {
      final response = await OnlineBookingService.getAvailableTimeSlots(
        salonId: widget.bookingState.salonId,
        date: dateStr,
        serviceIds: widget.bookingState.selectedServiceIds,
      );

      if (response.success && response.data != null) {
        if (mounted) {
          setState(() {
            _availabilityCache[dateStr] = response.data!.availableSlots;
            if (response.data!.availableSlots.isNotEmpty) {
              _daysWithAvailability.add(dateStr);
            }
          });
        }
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ Error loading availability for $dateStr: $e');
    }
  }

  Future<void> _loadAvailableSlots() async {
    if (_selectedDate == null) return;

    setState(() {
      _isLoadingSlots = true;
      _errorMessage = null;
      _selectedTimeSlot = null;
    });

    try {
      final dateStr = DateFormat('yyyy-MM-dd').format(_selectedDate!);

      // Check cache first
      if (_availabilityCache.containsKey(dateStr)) {
        setState(() {
          _availableSlots = _availabilityCache[dateStr]!;
          _isLoadingSlots = false;
        });
        return;
      }

      final response = await OnlineBookingService.getAvailableTimeSlots(
        salonId: widget.bookingState.salonId,
        date: dateStr,
        serviceIds: widget.bookingState.selectedServiceIds,
      );

      if (response.success && response.data != null) {
        setState(() {
          _availableSlots = response.data!.availableSlots;
          _availabilityCache[dateStr] = response.data!.availableSlots;
          if (response.data!.availableSlots.isNotEmpty) {
            _daysWithAvailability.add(dateStr);
          }
          _isLoadingSlots = false;
        });
      } else {
        setState(() {
          _errorMessage = response.error ?? 'Nu s-au putut încărca intervalele disponibile';
          _isLoadingSlots = false;
        });
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ Error loading time slots: $e');
      setState(() {
        _errorMessage = 'A apărut o eroare la încărcarea intervalelor';
        _isLoadingSlots = false;
      });
    }
  }

  Future<void> _createProtoAppointment() async {
    if (_selectedDate == null || _selectedTimeSlot == null) return;

    setState(() {
      _isLoadingSlots = true;
      _errorMessage = null;
    });

    try {
      final dateStr = DateFormat('yyyy-MM-dd').format(_selectedDate!);
      final request = CreateProtoAppointmentRequest(
        date: dateStr,
        startTime: _selectedTimeSlot!.startTime,
        endTime: _selectedTimeSlot!.endTime,
        serviceIds: widget.bookingState.selectedServiceIds,
      );

      final response = await OnlineBookingService.createProtoAppointment(
        salonId: widget.bookingState.salonId,
        request: request,
      );

      if (response.success && response.data != null) {
        final updatedState = widget.bookingState.copyWith(
          selectedDate: dateStr,
          selectedStartTime: _selectedTimeSlot!.startTime,
          selectedEndTime: _selectedTimeSlot!.endTime,
          appointmentId: response.data!.appointmentId,
          appointmentExpiresAt: response.data!.expirationTime,
        );

        if (mounted) {
          Navigator.pop(context, updatedState);
        }
      } else {
        setState(() {
          _errorMessage = response.error ?? 'Nu s-a putut rezerva intervalul';
          _isLoadingSlots = false;
        });
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ Error creating proto-appointment: $e');
      setState(() {
        _errorMessage = 'A apărut o eroare la rezervarea intervalului';
        _isLoadingSlots = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Alege ora'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Week selector
          _buildWeekSelector(),

          // Time slots
          Expanded(
            child: _buildTimeSlotsSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildWeekSelector() {
    final weekEnd = _currentWeekStart.add(const Duration(days: 6));
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);

    // Format date range (e.g., "27-2 oct")
    String dateRangeText;
    if (_currentWeekStart.month == weekEnd.month) {
      dateRangeText = '${_currentWeekStart.day}-${weekEnd.day} ${_getMonthAbbreviation(weekEnd.month)}';
    } else {
      dateRangeText = '${_currentWeekStart.day} ${_getMonthAbbreviation(_currentWeekStart.month)} - ${weekEnd.day} ${_getMonthAbbreviation(weekEnd.month)}';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: Column(
        children: [
          // Date range and "Următor" button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                dateRangeText,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _currentWeekStart = _currentWeekStart.add(const Duration(days: 7));
                  });
                  _preloadWeekAvailability(_currentWeekStart);
                },
                icon: Text(
                  'Următor',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: _primaryColor,
                  ),
                ),
                label: Icon(Icons.chevron_right, size: 24, color: _primaryColor),
                style: TextButton.styleFrom(
                  foregroundColor: _primaryColor,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Week days
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(7, (index) {
              final date = _currentWeekStart.add(Duration(days: index));
              final isSelected = _selectedDate != null &&
                  date.year == _selectedDate!.year &&
                  date.month == _selectedDate!.month &&
                  date.day == _selectedDate!.day;
              final isPast = date.isBefore(todayStart);
              final dateStr = DateFormat('yyyy-MM-dd').format(date);
              final hasAvailability = _daysWithAvailability.contains(dateStr);

              return _buildDayColumn(
                dayName: _getDayAbbreviation(date.weekday),
                dayNumber: date.day,
                isSelected: isSelected,
                isPast: isPast,
                hasAvailability: hasAvailability,
                onTap: isPast
                    ? null
                    : () {
                        setState(() {
                          _selectedDate = date;
                        });
                        _loadAvailableSlots();
                      },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildDayColumn({
    required String dayName,
    required int dayNumber,
    required bool isSelected,
    required bool isPast,
    required bool hasAvailability,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Text(
            dayName,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isPast ? Colors.grey[400] : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: isSelected ? _primaryColor : Colors.transparent,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                dayNumber.toString(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected
                      ? Colors.white
                      : isPast
                          ? Colors.grey[400]
                          : Colors.black87,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getDayAbbreviation(int weekday) {
    switch (weekday) {
      case 1:
        return 'Lu.';
      case 2:
        return 'Ma.';
      case 3:
        return 'Mi.';
      case 4:
        return 'Jo.';
      case 5:
        return 'Vi.';
      case 6:
        return 'Sâ.';
      case 7:
        return 'Du.';
      default:
        return '';
    }
  }

  String _getDayName(DateTime date) {
    switch (date.weekday) {
      case 1:
        return 'luni';
      case 2:
        return 'marți';
      case 3:
        return 'miercuri';
      case 4:
        return 'joi';
      case 5:
        return 'vineri';
      case 6:
        return 'sâmbătă';
      case 7:
        return 'duminică';
      default:
        return '';
    }
  }

  String _getMonthAbbreviation(int month) {
    switch (month) {
      case 1:
        return 'ian';
      case 2:
        return 'feb';
      case 3:
        return 'mar';
      case 4:
        return 'apr';
      case 5:
        return 'mai';
      case 6:
        return 'iun';
      case 7:
        return 'iul';
      case 8:
        return 'aug';
      case 9:
        return 'sep';
      case 10:
        return 'oct';
      case 11:
        return 'nov';
      case 12:
        return 'dec';
      default:
        return '';
    }
  }

  Widget _buildTimeSlotsSection() {
    if (_isLoadingSlots) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadAvailableSlots,
                icon: const Icon(Icons.refresh),
                label: const Text('Încearcă din nou'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_availableSlots.isEmpty) {
      // Show "no slots" message with button to go to next available day
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.event_busy_outlined,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 24),
              Text(
                _nextAvailableDay != null
                    ? '${widget.salonPreferences.businessName} nu are timp disponibil în această zi. Următoarea este ${_getDayName(_nextAvailableDay!)} ${DateFormat('d MMM', 'ro_RO').format(_nextAvailableDay!)}.'
                    : 'Nu sunt intervale disponibile în această zi.',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              if (_nextAvailableDay != null) ...[
                const SizedBox(height: 32),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _selectedDate = _nextAvailableDay;
                        _currentWeekStart = _getWeekStart(_nextAvailableDay!);
                      });
                      _loadAvailableSlots();
                      _preloadWeekAvailability(_currentWeekStart);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      'Mergi la următoarea zi disponibilă',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }

    // Show available slots
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
          child: Text(
            'Ore disponibile ${DateFormat('d MMM', 'ro_RO').format(_selectedDate!)}.',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            itemCount: _availableSlots.length,
            itemBuilder: (context, index) {
              final slot = _availableSlots[index];
              final isSelected = _selectedTimeSlot == slot;
              return _buildTimeSlotCard(slot, isSelected);
            },
          ),
        ),
        if (_selectedTimeSlot != null) _buildContinueButton(),
      ],
    );
  }

  Widget _buildTimeSlotCard(TimeSlot slot, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        elevation: 0,
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedTimeSlot = slot;
            });
            // Auto-continue after selection
            _createProtoAppointment();
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[200]!,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  slot.startTime,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey[400],
                  size: 24,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContinueButton() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoadingSlots ? null : _createProtoAppointment,
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
            child: _isLoadingSlots
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Continuă cu ${_selectedTimeSlot!.startTime}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward, size: 20),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
