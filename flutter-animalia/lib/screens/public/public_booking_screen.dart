import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:carousel_slider/carousel_slider.dart' as carousel;
import 'dart:ui';

import '../../models/salon_web_preferences.dart';
import '../../models/service.dart';
import '../../models/online_booking.dart';
import '../../services/salon_web_preferences_service.dart';
import '../../services/public_salon_service.dart';
import '../../utils/debug_logger.dart';
import '../../config/environment.dart';
import 'booking/service_selection_screen.dart';
import 'booking/datetime_selection_screen.dart';
import 'booking/client_information_screen.dart';

/// Public booking screen - accessible without authentication
/// Displays salon information from SalonWebPreferences and allows booking
class PublicBookingScreen extends StatefulWidget {
  final String bookingUrl;

  const PublicBookingScreen({
    super.key,
    required this.bookingUrl,
  });

  @override
  State<PublicBookingScreen> createState() => _PublicBookingScreenState();
}

class _PublicBookingScreenState extends State<PublicBookingScreen> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  String? _errorMessage;
  SalonWebPreferences? _salonPreferences;
  List<Service> _services = [];
  bool _isLoadingServices = true;
  int _currentPhotoIndex = 0;
  final carousel.CarouselSliderController _carouselController = carousel.CarouselSliderController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _loadSalonInformation();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSalonInformation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      DebugLogger.logShowcase('🌐 Loading public salon info for URL: ${widget.bookingUrl}');

      final response = await SalonWebPreferencesService.getPublicWebPreferences(widget.bookingUrl);

      if (response.success && response.data != null) {
        setState(() {
          _salonPreferences = response.data;
          _isLoading = false;
        });
        _animationController.forward();
        DebugLogger.logShowcase('✅ Loaded salon info: ${_salonPreferences?.businessName}');

        // Load services after salon info is loaded
        _loadServices();
      } else {
        setState(() {
          _errorMessage = response.error ?? 'Salonul nu a fost găsit';
          _isLoading = false;
        });
        DebugLogger.logShowcase('❌ Failed to load salon info: ${response.error}');
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'A apărut o eroare la încărcarea informațiilor salonului';
        _isLoading = false;
      });
      DebugLogger.logShowcase('❌ Exception loading salon info: $e');
    }
  }

  Future<void> _loadServices() async {
    if (_salonPreferences == null) return;

    setState(() {
      _isLoadingServices = true;
    });

    try {
      final response = await PublicSalonService.getPublicServices(_salonPreferences!.salonId);

      print(response.data);
      if (response.success && response.data != null) {
        setState(() {
          _services = response.data!;
          _isLoadingServices = false;
        });
      } else {
        setState(() {
          _isLoadingServices = false;
        });
        DebugLogger.logShowcase('❌ Failed to load services: ${response.error}');
      }
    } catch (e) {
      setState(() {
        _isLoadingServices = false;
      });
      DebugLogger.logShowcase('❌ Exception loading services: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildSkeletonLoader();
    }

    if (_errorMessage != null || _salonPreferences == null) {
      return _buildErrorView();
    }

    // Check if booking is disabled
    if (!_salonPreferences!.onlineBookingEnabled) {
      return _buildNotFoundView();
    }

    return _buildSalonInformation();
  }

  Widget _buildSkeletonLoader() {
    return CustomScrollView(
      slivers: [
        // Header skeleton
        SliverToBoxAdapter(
          child: Container(
            height: 400,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.grey[300]!,
                  Colors.grey[200]!,
                ],
              ),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Se încarcă...',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Content skeleton
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _buildSkeletonCard(height: 200),
                const SizedBox(height: 16),
                _buildSkeletonCard(height: 150),
                const SizedBox(height: 16),
                _buildSkeletonCard(height: 150),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSkeletonCard({required double height}) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.grey[200]!,
                      Colors.grey[100]!,
                      Colors.grey[200]!,
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                ),
              ),
            ),
            Positioned.fill(
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 1500),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(-1.0, 0.0),
                    end: Alignment(1.0, 0.0),
                    colors: [
                      Colors.grey[200]!.withOpacity(0.0),
                      Colors.white.withOpacity(0.3),
                      Colors.grey[200]!.withOpacity(0.0),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotFoundView() {
    return Semantics(
      label: 'Pagina nu este disponibilă',
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.web_asset_off_rounded,
                  size: 80,
                  color: Colors.orange[700],
                ),
              ),
              const SizedBox(height: 32),
              Text(
                'Pagina nu este disponibilă',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Acest salon nu acceptă programări online momentan.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              if (_salonPreferences?.contactPhone.isNotEmpty ?? false) ...[
                const SizedBox(height: 32),
                Text(
                  'Pentru programări, vă rugăm să ne contactați:',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 16),
                Semantics(
                  label: 'Sună la ${_salonPreferences!.contactPhone}',
                  button: true,
                  child: ElevatedButton.icon(
                    onPressed: () => _makePhoneCall(_salonPreferences!.contactPhone),
                    icon: const Icon(Icons.phone),
                    label: Text(_salonPreferences!.contactPhone),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Semantics(
      label: 'Eroare la încărcarea paginii',
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.error_outline_rounded,
                  size: 80,
                  color: Colors.red[700],
                ),
              ),
              const SizedBox(height: 32),
              Text(
                'Oops! Ceva nu a mers bine',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage ?? 'Eroare necunoscută',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Semantics(
                label: 'Încearcă să încarci din nou',
                button: true,
                child: ElevatedButton.icon(
                  onPressed: _loadSalonInformation,
                  icon: const Icon(Icons.refresh_rounded),
                  label: const Text('Încearcă din nou'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSalonInformation() {
    final prefs = _salonPreferences!;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: CustomScrollView(
        slivers: [
          // Cover Photo Carousel at the top
          if (prefs.websitePhotos.isNotEmpty)
            SliverToBoxAdapter(
              child: _buildCoverPhotoCarousel(prefs),
            ),

          // Header Section with Salon Name and Logo
          SliverToBoxAdapter(
            child: _buildHeader(prefs),
          ),

          // Services Section - Primary Focus
          SliverToBoxAdapter(
            child: _buildServicesSection(prefs),
          ),

          // Contact Section with Map and Contact Info
          SliverToBoxAdapter(
            child: _buildContactSection(prefs),
          ),

          // Footer
          SliverToBoxAdapter(
            child: _buildFooter(prefs),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: 80),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(SalonWebPreferences prefs) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(160, 16, 24, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            prefs.businessName,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: _parseColor(prefs.primaryColor),
              letterSpacing: -0.5,
            ),
          ),
          if (prefs.businessDescription.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              prefs.businessDescription,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                height: 1.3,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCoverPhotoCarousel(SalonWebPreferences prefs) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Carousel
        SizedBox(
          height: 280,
          width: double.infinity,
          child: carousel.CarouselSlider(
            carouselController: _carouselController,
            options: carousel.CarouselOptions(
              height: 280,
              viewportFraction: 1.0,
              enlargeCenterPage: false,
              autoPlay: prefs.websitePhotos.length > 1,
              autoPlayInterval: const Duration(seconds: 5),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.fastOutSlowIn,
              onPageChanged: (index, reason) {
                setState(() {
                  _currentPhotoIndex = index;
                });
              },
            ),
            items: prefs.websitePhotos.map((photo) {
              return Builder(
                builder: (BuildContext context) {
                  return Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                    ),
                    child: Image.network(
                      photo,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported_rounded,
                              size: 64,
                              color: Colors.grey[500],
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              );
            }).toList(),
          ),
        ),

        // Gradient overlay at bottom for better readability
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.4),
                ],
              ),
            ),
          ),
        ),

        // Page indicators
        if (prefs.websitePhotos.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: prefs.websitePhotos.asMap().entries.map((entry) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: _currentPhotoIndex == entry.key ? 32 : 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: _currentPhotoIndex == entry.key
                        ? Colors.white
                        : Colors.white.withOpacity(0.5),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),

        // Logo overlapping the bottom of the cover (Facebook style)
        Positioned(
          bottom: -60, // Half of the logo height to overlap
          left: 24,
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 4,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: prefs.logoUrl.isNotEmpty
                ? ClipOval(
                    child: Image.network(
                      prefs.logoUrl,
                      height: 120,
                      width: 120,
                      fit: BoxFit.cover,
                      errorBuilder: (_, __, ___) => Container(
                        height: 120,
                        width: 120,
                        decoration: BoxDecoration(
                          color: _parseColor(prefs.primaryColor).withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.pets_rounded,
                          size: 60,
                          color: _parseColor(prefs.primaryColor),
                        ),
                      ),
                    ),
                  )
                : Container(
                    height: 120,
                    width: 120,
                    decoration: BoxDecoration(
                      color: _parseColor(prefs.primaryColor).withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.pets_rounded,
                      size: 60,
                      color: _parseColor(prefs.primaryColor),
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactSection(SalonWebPreferences prefs) {
    return Container(
      color: Colors.grey[50],
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Row(
            children: [
              Icon(
                Icons.contact_phone_rounded,
                color: _parseColor(prefs.primaryColor),
                size: 28,
              ),
              const SizedBox(width: 12),
              const Text(
                'Contactează-ne',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  letterSpacing: -0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 32),

          // Interactive Map
          if (prefs.businessAddress.isNotEmpty) ...[
            GestureDetector(
              onTap: () => _openMap(prefs.businessAddress),
              child: Container(
                height: 250,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _parseColor(prefs.primaryColor).withOpacity(0.3),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 12,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(18),
                  child: Stack(
                    children: [
                      // Google Maps Static API preview
                      Image.network(
                        _getStaticMapUrl(prefs.businessAddress),
                        width: double.infinity,
                        height: 250,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[200],
                            child: Center(
                              child: Icon(
                                Icons.map_rounded,
                                size: 80,
                                color: Colors.grey[400],
                              ),
                            ),
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Container(
                            color: Colors.grey[200],
                            child: Center(
                              child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                        loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            ),
                          );
                        },
                      ),
                      // Tap overlay
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.7),
                              ],
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.directions_rounded,
                                color: Colors.white,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Apasă pentru direcții',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Contact Information Cards
          _buildContactInfoCard(
            icon: Icons.location_on_rounded,
            label: 'Adresă',
            value: prefs.businessAddress,
            onTap: () => _openMap(prefs.businessAddress),
            color: _parseColor(prefs.primaryColor),
          ),
          const SizedBox(height: 12),

          if (prefs.contactPhone.isNotEmpty) ...[
            _buildContactInfoCard(
              icon: Icons.phone_rounded,
              label: 'Telefon',
              value: prefs.contactPhone,
              onTap: () => _makePhoneCall(prefs.contactPhone),
              color: Colors.green,
            ),
            const SizedBox(height: 12),
          ],

          if (prefs.contactEmail.isNotEmpty) ...[
            _buildContactInfoCard(
              icon: Icons.email_rounded,
              label: 'Email',
              value: prefs.contactEmail,
              onTap: () => _sendEmail(prefs.contactEmail),
              color: Colors.blue,
            ),
            const SizedBox(height: 12),
          ],

          // Business Hours
          if (prefs.businessHours.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildBusinessHoursCard(prefs),
          ],

          // Social Media
          if (prefs.facebookLink.isNotEmpty ||
              prefs.instagramLink.isNotEmpty ||
              prefs.tiktokLink.isNotEmpty) ...[
            const SizedBox(height: 24),
            _buildSocialMediaSection(prefs),
          ],
        ],
      ),
    );
  }

  Widget _buildContactInfoCard({
    required IconData icon,
    required String label,
    required String value,
    required VoidCallback onTap,
    required Color color,
  }) {
    if (value.isEmpty) return const SizedBox.shrink();

    return Semantics(
      label: '$label: $value',
      button: true,
      hint: 'Apasă pentru a contacta',
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey[200]!),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios_rounded, size: 18, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBusinessHoursCard(SalonWebPreferences prefs) {
    final now = DateTime.now();
    final todayKey = _getTodayKey(now);
    final todayHours = prefs.businessHours[todayKey];
    final isOpenNow = _isOpenNow(todayHours, now);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time_rounded,
                color: _parseColor(prefs.primaryColor),
                size: 24,
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Program de lucru',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: isOpenNow ? Colors.green[50] : Colors.red[50],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: isOpenNow ? Colors.green : Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      isOpenNow ? 'Deschis' : 'Închis',
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: isOpenNow ? Colors.green[700] : Colors.red[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Show all days
          ...prefs.businessHours.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: _buildScheduleRow(entry.key, entry.value),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildSocialMediaSection(SalonWebPreferences prefs) {
    final socialLinks = <Map<String, dynamic>>[];

    if (prefs.facebookLink.isNotEmpty) {
      socialLinks.add({
        'icon': Icons.facebook_rounded,
        'label': 'Facebook',
        'url': prefs.facebookLink,
        'color': const Color(0xFF1877F2),
      });
    }

    if (prefs.instagramLink.isNotEmpty) {
      socialLinks.add({
        'icon': Icons.camera_alt_rounded,
        'label': 'Instagram',
        'url': prefs.instagramLink,
        'color': const Color(0xFFE4405F),
      });
    }

    if (prefs.tiktokLink.isNotEmpty) {
      socialLinks.add({
        'icon': Icons.music_note_rounded,
        'label': 'TikTok',
        'url': prefs.tiktokLink,
        'color': Colors.black,
      });
    }

    if (socialLinks.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.share_rounded,
              color: _parseColor(prefs.primaryColor),
              size: 24,
            ),
            const SizedBox(width: 12),
            const Text(
              'Urmărește-ne',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: socialLinks.map((link) {
            return _buildSocialButton(
              icon: link['icon'] as IconData,
              label: link['label'] as String,
              url: link['url'] as String,
              color: link['color'] as Color,
            );
          }).toList(),
        ),
      ],
    );
  }

  String _getTodayKey(DateTime now) {
    final dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return dayNames[now.weekday - 1];
  }

  bool _isOpenNow(Map<String, dynamic>? todayHours, DateTime now) {
    if (todayHours == null) return false;

    final isOpen = todayHours['isOpen']?.toString() == 'true';
    if (!isOpen) return false;

    try {
      final openTime = todayHours['open']?.toString() ?? '';
      final closeTime = todayHours['close']?.toString() ?? '';

      if (openTime.isEmpty || closeTime.isEmpty) return false;

      final openParts = openTime.split(':');
      final closeParts = closeTime.split(':');

      final openHour = int.parse(openParts[0]);
      final openMinute = int.parse(openParts[1]);
      final closeHour = int.parse(closeParts[0]);
      final closeMinute = int.parse(closeParts[1]);

      final currentMinutes = now.hour * 60 + now.minute;
      final openMinutes = openHour * 60 + openMinute;
      final closeMinutes = closeHour * 60 + closeMinute;

      return currentMinutes >= openMinutes && currentMinutes <= closeMinutes;
    } catch (e) {
      return false;
    }
  }



  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required String url,
    required Color color,
  }) {
    return Semantics(
      label: 'Vizitează $label',
      button: true,
      link: true,
      child: InkWell(
        onTap: () => _openUrl(url),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 10),
              Text(
                label,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildScheduleRow(String day, Map<String, dynamic> hours) {
    final isOpen = hours['isOpen']?.toString() == 'true';
    final openTime = hours['open']?.toString() ?? '';
    final closeTime = hours['close']?.toString() ?? '';
    final dayName = _getDayName(day);

    // Check if today
    final now = DateTime.now();
    final isToday = _isToday(day, now);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            isToday ? '$dayName (azi)' : dayName,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            isOpen ? '$openTime - $closeTime' : 'Închis',
            style: TextStyle(
              fontSize: 14,
              color: isOpen ? Colors.black87 : Colors.grey,
              fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  bool _isToday(String day, DateTime now) {
    final dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    final todayIndex = now.weekday - 1; // Monday = 0
    return dayNames[todayIndex] == day.toLowerCase();
  }

  String _getDayName(String day) {
    final dayNames = {
      'monday': 'Luni',
      'tuesday': 'Marți',
      'wednesday': 'Miercuri',
      'thursday': 'Joi',
      'friday': 'Vineri',
      'saturday': 'Sâmbătă',
      'sunday': 'Duminică',
    };
    return dayNames[day.toLowerCase()] ?? day;
  }



  Widget _buildServicesSection(SalonWebPreferences prefs) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(top: 8, bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                Icon(
                  Icons.spa_rounded,
                  color: _parseColor(prefs.primaryColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Serviciile noastre',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      letterSpacing: -0.5,
                    ),
                  ),
                ),
                if (_services.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _parseColor(prefs.primaryColor).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_services.length}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _parseColor(prefs.primaryColor),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Loading state
          if (_isLoadingServices)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(48.0),
                child: CircularProgressIndicator(),
              ),
            )
          // Empty state
          else if (_services.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(48.0),
                child: Column(
                  children: [
                    Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'Momentan nu sunt servicii disponibile',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          // Horizontal scrollable services
          else
            SizedBox(
              height: 220,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _services.length,
                itemBuilder: (context, index) {
                  return _buildServiceCard(
                    service: _services[index],
                    prefs: prefs,
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildServiceCard({
    required Service service,
    required SalonWebPreferences prefs,
  }) {
    return Container(
      width: 280,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _parseColor(prefs.primaryColor).withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: _parseColor(prefs.primaryColor).withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Service Header - Compact
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _parseColor(prefs.primaryColor).withOpacity(0.1),
                  _parseColor(prefs.primaryColor).withOpacity(0.05),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(14),
                topRight: Radius.circular(14),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Service Name
                Text(
                  service.name,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _parseColor(prefs.primaryColor),
                    height: 1.2,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),

                // Price and Duration
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.payments_rounded,
                      size: 16,
                      color: _parseColor(prefs.primaryColor),
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        service.formattedPrice,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _parseColor(prefs.primaryColor),
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    Icon(
                      Icons.access_time_rounded,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '${service.duration} min',
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Service Body - Compact
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Description - Limited to 2 lines
                  if (service.description.isNotEmpty)
                    Text(
                      service.description,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                  const Spacer(),

                  // Book Button - Compact
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _showBookingDialog(service, prefs),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _parseColor(prefs.primaryColor),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.calendar_today_rounded, size: 16),
                          SizedBox(width: 8),
                          Text(
                            'Programează',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _parseColor(String hexColor) {
    try {
      final hex = hexColor.replaceAll('#', '');
      return Color(int.parse('FF$hex', radix: 16));
    } catch (e) {
      return Colors.blue[700]!;
    }
  }

  void _showBookingDialog(Service service, SalonWebPreferences prefs) async {
    if (_salonPreferences == null) return;

    // Create initial booking state with the selected service
    final initialState = BookingFlowState(
      salonId: _salonPreferences!.salonId,
      selectedServiceIds: [service.id],
    );

    // Navigate to service selection screen
    final serviceSelectionResult = await Navigator.push<BookingFlowState>(
      context,
      MaterialPageRoute(
        builder: (context) => ServiceSelectionScreen(
          salonId: _salonPreferences!.salonId,
          availableServices: _services,
          salonPreferences: _salonPreferences!,
          initialState: initialState,
        ),
      ),
    );

    if (serviceSelectionResult != null && mounted) {
      // Navigate to date/time selection screen
      final dateTimeResult = await Navigator.push<BookingFlowState>(
        context,
        MaterialPageRoute(
          builder: (context) => DateTimeSelectionScreen(
            bookingState: serviceSelectionResult,
            salonPreferences: _salonPreferences!,
          ),
        ),
      );

      if (dateTimeResult != null && mounted) {
        // Navigate to client information form screen
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ClientInformationScreen(
              bookingState: dateTimeResult,
              salonPreferences: _salonPreferences!,
            ),
          ),
        );
      }
    }
  }

  void _showBookingDialogOld(Service service, SalonWebPreferences prefs) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.calendar_month, color: _parseColor(prefs.primaryColor)),
              const SizedBox(width: 12),
              const Expanded(child: Text('Programare')),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  service.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${service.formattedPrice} • ${service.duration} minute',
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Funcționalitatea completă de programare va fi disponibilă în curând!',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue[900],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Pentru programări, vă rugăm să ne contactați:',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 12),
                if (prefs.contactPhone.isNotEmpty)
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      _makePhoneCall(prefs.contactPhone);
                    },
                    child: Row(
                      children: [
                        Icon(Icons.phone, color: Colors.green[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          prefs.contactPhone,
                          style: TextStyle(
                            fontSize: 15,
                            color: Colors.blue[700],
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Închide'),
            ),
            if (prefs.contactPhone.isNotEmpty)
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                  _makePhoneCall(prefs.contactPhone);
                },
                icon: const Icon(Icons.phone, size: 18),
                label: const Text('Sună acum'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildFooter(SalonWebPreferences prefs) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _parseColor(prefs.primaryColor).withOpacity(0.1),
            _parseColor(prefs.primaryColor).withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _parseColor(prefs.primaryColor).withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          if (prefs.logoUrl.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                prefs.logoUrl,
                height: 60,
                width: 60,
                fit: BoxFit.cover,
                errorBuilder: (_, __, ___) => Icon(
                  Icons.pets_rounded,
                  size: 40,
                  color: _parseColor(prefs.primaryColor),
                ),
              ),
            ),
          if (prefs.logoUrl.isNotEmpty) const SizedBox(height: 16),

          Text(
            prefs.businessName,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _parseColor(prefs.primaryColor),
            ),
            textAlign: TextAlign.center,
          ),

          if (prefs.businessAddress.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.location_on_rounded,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    prefs.businessAddress,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ],

          const SizedBox(height: 20),
          const Divider(),
          const SizedBox(height: 12),

          // Cancellation Policy
          Row(
            children: [
              Icon(
                Icons.info_outline_rounded,
                size: 18,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Politică de anulare: ${_getCancellationPolicyText(prefs.cancellationPolicy)}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Booking Acceptance
          Row(
            children: [
              Icon(
                Icons.check_circle_outline_rounded,
                size: 18,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Acceptare programări: ${_getBookingAcceptanceText(prefs.bookingAcceptance)}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          Text(
            '© ${DateTime.now().year} ${prefs.businessName}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getCancellationPolicyText(CancellationPolicy policy) {
    switch (policy) {
      case CancellationPolicy.HOURS_24:
        return 'Anulare cu 24 ore înainte';
      case CancellationPolicy.HOURS_48:
        return 'Anulare cu 48 ore înainte';
      case CancellationPolicy.HOURS_72:
        return 'Anulare cu 72 ore înainte';
      case CancellationPolicy.NO_CHANGES:
        return 'Fără anulare';
    }
  }

  String _getBookingAcceptanceText(BookingAcceptance acceptance) {
    switch (acceptance) {
      case BookingAcceptance.automatic:
        return 'Automată';
      case BookingAcceptance.manual:
        return 'Manuală (necesită confirmare)';
    }
  }

  Widget _buildFloatingActionButton() {
    final prefs = _salonPreferences;
    if (prefs == null) return const SizedBox.shrink();

    return Semantics(
      label: 'Contactează salonul',
      button: true,
      child: FloatingActionButton.extended(
        onPressed: () {
          // Scroll to services section or show contact options
          if (prefs.contactPhone.isNotEmpty) {
            _makePhoneCall(prefs.contactPhone);
          }
        },
        backgroundColor: _parseColor(prefs.primaryColor),
        icon: const Icon(Icons.phone_rounded),
        label: const Text(
          'Contactează-ne',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        elevation: 6,
        heroTag: 'contact_fab',
      ),
    );
  }

  // Helper method to generate Google Maps Static API URL
  String _getStaticMapUrl(String address) {
    final encodedAddress = Uri.encodeComponent(address);
    final apiKey = EnvironmentConfig.googleMapsApiKey;
    return 'https://maps.googleapis.com/maps/api/staticmap?center=$encodedAddress&zoom=15&size=600x300&markers=color:red%7C$encodedAddress&scale=2&key=$apiKey';
  }

  // Helper methods for launching external apps
  Future<void> _openMap(String address) async {
    final encodedAddress = Uri.encodeComponent(address);
    final url = Uri.parse('https://www.google.com/maps/search/?api=1&query=$encodedAddress');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final url = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  Future<void> _sendEmail(String email) async {
    final url = Uri.parse('mailto:$email');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  Future<void> _openUrl(String urlString) async {
    final url = Uri.parse(urlString);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }
}
