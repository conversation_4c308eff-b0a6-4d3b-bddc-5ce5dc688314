import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import '../../../l10n/app_localizations.dart';
import '../../../models/salon.dart';
import '../../../models/salon_settings.dart';
import '../../../models/salon_web_preferences.dart';
import '../../../models/image_upload_response.dart';
import '../../../models/working_hours_settings.dart';
import '../../../models/service.dart';
import '../../../services/api_service.dart';
import '../../../services/auth/auth_service.dart';
import '../../../services/salon_service.dart';
import '../../../services/salon_web_preferences_service.dart';
import '../../../services/service_management_service.dart';
import '../../../services/settings_service.dart';
import '../../../services/ui_notification_service.dart';
import '../../../services/url_launcher_service.dart';
import '../../../widgets/common/standard_form_field.dart';
import '../../../widgets/permission_guard.dart';
import '../../../widgets/address_selection/location_selection_button.dart';
import '../../../widgets/dialogs/booking_qr_code_dialog.dart';
import '../../../widgets/schedule/advanced_schedule_step.dart';
import 'booking_page_summary_screen.dart';

/// Screen for managing salon website settings with multi-step form
class WebsiteManagementScreen extends StatefulWidget {
  const WebsiteManagementScreen({super.key});

  @override
  State<WebsiteManagementScreen> createState() => _WebsiteManagementScreenState();
}

/// Enum for website management steps
enum WebsiteManagementStep {
  basicInfo,
  customizeBooking,
  businessHours,
  extraInfo,
  bookingAcceptance,
}

class _WebsiteManagementScreenState extends State<WebsiteManagementScreen> {
  final _formKey = GlobalKey<FormState>();
  final PageController _pageController = PageController();

  // Current step
  WebsiteManagementStep _currentStep = WebsiteManagementStep.basicInfo;

  // Loading states
  bool _isLoading = false;
  bool _isSaving = false;
  SalonSettings? _currentSettings;
  Salon? _currentSalon;
  Map<String, dynamic>? _currentUser;
  SalonWebPreferences? _currentWebPreferences;

  // Basic Info Controllers
  final _bookingWebsiteController = TextEditingController();
  final _bookingSlugController = TextEditingController();
  final _businessNameController = TextEditingController();
  final _businessDescriptionController = TextEditingController();
  final _emailController = TextEditingController();
  final _facebookController = TextEditingController();
  final _instagramController = TextEditingController();
  final _tiktokController = TextEditingController();

  // Phone number state
  String _completePhoneNumber = '';
  String _initialCountryCode = 'RO';

  // Business address
  String? _selectedAddress;

  // Business hours (simplified for now)
  Map<String, Map<String, String>> _businessHours = {
    'monday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'tuesday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'wednesday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'thursday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'friday': {'open': '09:00', 'close': '18:00', 'isOpen': 'true'},
    'saturday': {'open': '09:00', 'close': '16:00', 'isOpen': 'true'},
    'sunday': {'open': '10:00', 'close': '14:00', 'isOpen': 'false'},
  };

  // Extra info
  CancellationPolicy _cancellationPolicy = CancellationPolicy.HOURS_24;

  // Booking acceptance
  BookingAcceptance _bookingAcceptance = BookingAcceptance.automatic;

  // Online Booking Settings
  bool _onlineBookingEnabled = true;
  List<String> _selectedServiceIds = [];
  List<Service> _availableServices = [];
  bool _isLoadingServices = false;
  final _bookingPasswordController = TextEditingController();

  // Photo management
  final ImagePicker _imagePicker = ImagePicker();
  List<XFile> _selectedPhotos = [];
  // Previews for the selected photos (MemoryImage for web, FileImage for native)
  List<ImageProvider> _selectedPhotoPreviews = [];
  List<String> _existingPhotoUrls = [];
  bool _isUploadingPhotos = false;

  // Logo and customization
  String? _logoUrl;
  Color _selectedPrimaryColor = const Color(0xFF6366F1); // Default indigo
  bool _isUploadingLogo = false;

  @override
  void initState() {
    super.initState();
    _loadLastCountryCode();
    _loadCurrentSettings();

    // Add listener to business name field to auto-update booking website link
    _businessNameController.addListener(_updateBookingWebsiteLink);
  }

  /// Load the last used country code from SharedPreferences
  Future<void> _loadLastCountryCode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCountryCode = prefs.getString('last_country_code');
      if (lastCountryCode != null && mounted) {
        setState(() {
          _initialCountryCode = lastCountryCode;
        });
      }
    } catch (e) {
      print('Error loading last country code: $e');
    }
  }

  /// Save the country code to SharedPreferences
  Future<void> _saveLastCountryCode(String countryCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_country_code', countryCode);
    } catch (e) {
      print('Error saving country code: $e');
    }
  }

  @override
  void dispose() {
    // Remove listener before disposing
    _businessNameController.removeListener(_updateBookingWebsiteLink);

    _pageController.dispose();
    _bookingWebsiteController.dispose();
    _bookingSlugController.dispose();
    _businessNameController.dispose();
    _businessDescriptionController.dispose();
    _emailController.dispose();
    _facebookController.dispose();
    _instagramController.dispose();
    _tiktokController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load salon settings, salon data, current user, and web preferences in parallel
      final settingsResponse = await SettingsService.getSalonSettings();
      final salonResponse = await SalonService.getCurrentUserSalon();
      final userResponse = await AuthService.getCurrentUser();
      final webPreferencesResponse = await SalonWebPreferencesService.getWebPreferences();

      if (settingsResponse.success && settingsResponse.data != null) {
        _currentSettings = settingsResponse.data!;
      }

      if (salonResponse.success && salonResponse.data != null) {
        _currentSalon = salonResponse.data!;
      }

      if (userResponse.success && userResponse.data != null) {
        _currentUser = userResponse.data!;
      }

      if (webPreferencesResponse.success && webPreferencesResponse.data != null) {
        _currentWebPreferences = webPreferencesResponse.data!;
      }

      _populateFormFields();
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: 'Error loading settings: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _populateFormFields() {
    String businessName = '';

    // First priority: Use existing web preferences if available
    if (_currentWebPreferences != null) {
      final prefs = _currentWebPreferences!;

      // Basic info from web preferences
      // Extract slug from the booking website URL
      final bookingUrl = prefs.bookingWebsiteSlug;
      if (bookingUrl.isNotEmpty) {
        // prefs now stores only the slug; display full URL in the controller
        final slug = bookingUrl.replaceFirst('https://animalia-programari.ro/book/', '');
        _bookingSlugController.text = slug;
        _bookingWebsiteController.text = 'https://animalia-programari.ro/book/$slug';
      }

      _businessNameController.text = prefs.businessName;
      _businessDescriptionController.text = prefs.businessDescription;
      _completePhoneNumber = prefs.contactPhone;
      _emailController.text = prefs.contactEmail;
      _facebookController.text = prefs.facebookLink;
      _instagramController.text = prefs.instagramLink;
      _tiktokController.text = prefs.tiktokLink;
      _selectedAddress = prefs.businessAddress.isNotEmpty ? prefs.businessAddress : null;

      // Photos
      _existingPhotoUrls = List.from(prefs.websitePhotos);

      // Logo and customization
      _logoUrl = prefs.logoUrl.isNotEmpty ? prefs.logoUrl : null;
      try {
        if (prefs.primaryColor.isNotEmpty) {
          _selectedPrimaryColor = Color(int.parse(prefs.primaryColor.replaceFirst('#', '0xFF')));
        }
      } catch (e) {
        // Use default color if parsing fails
        _selectedPrimaryColor = const Color(0xFF6366F1);
      }

      // Business hours
      _businessHours = prefs.businessHours.map(
        (key, value) => MapEntry(
          key,
          value.map((k, v) => MapEntry(k, v.toString())),
        ),
      );

      // Policies
      _cancellationPolicy = prefs.cancellationPolicy;
      _bookingAcceptance = prefs.bookingAcceptance;

      // Online Booking Settings
      _onlineBookingEnabled = prefs.onlineBookingEnabled;
      _selectedServiceIds = List.from(prefs.availableServiceIds);
      _bookingPasswordController.text = prefs.bookingPassword;

      businessName = prefs.businessName;
    } else {
      // Fallback: Populate from salon settings and salon data

      // Populate from salon settings (primary source for contact info)
      if (_currentSettings != null) {
        businessName = _currentSettings!.name;
        _businessNameController.text = _currentSettings!.name;
        _completePhoneNumber = _currentSettings!.phone;
        _emailController.text = _currentSettings!.email;
        _selectedAddress = _currentSettings!.address.isNotEmpty ? _currentSettings!.address : null;
      }

      // Populate from salon data (for description and other fields not in settings)
      if (_currentSalon != null) {
        // Use salon name if settings name is empty
        if (_businessNameController.text.isEmpty) {
          businessName = _currentSalon!.name;
          _businessNameController.text = _currentSalon!.name;
        }

        // Use salon description
        if (_currentSalon!.description != null && _currentSalon!.description!.isNotEmpty) {
          _businessDescriptionController.text = _currentSalon!.description!;
        }

        // Use salon address if settings address is empty
        if (_selectedAddress == null || _selectedAddress!.isEmpty) {
          _selectedAddress = _currentSalon!.address.isNotEmpty ? _currentSalon!.address : null;
        }

        // Use salon phone if settings phone is empty
        if (_completePhoneNumber.isEmpty && _currentSalon!.phone != null) {
          _completePhoneNumber = _currentSalon!.phone!;
        }

        // Use salon email if settings email is empty
        if (_emailController.text.isEmpty && _currentSalon!.email != null) {
          _emailController.text = _currentSalon!.email!;
        }
      }

      // Use current user's email as final fallback (owner's email)
      if (_emailController.text.isEmpty && _currentUser != null) {
        final userEmail = _currentUser!['email'] as String?;
        if (userEmail != null && userEmail.isNotEmpty) {
          _emailController.text = userEmail;
        }
      }

      // Generate booking website link using the business name
      if (businessName.isNotEmpty && _bookingSlugController.text.isEmpty) {
        final slug = _formatBusinessNameForUrl(businessName);
        _bookingSlugController.text = slug;
        _bookingWebsiteController.text = 'https://animalia-programari.ro/book/$slug';
      }
    }

    // Load available services
    _loadAvailableServices();
  }

  /// Load available services for the salon
  Future<void> _loadAvailableServices() async {
    setState(() {
      _isLoadingServices = true;
    });

    try {
      final servicesResponse = await ServiceManagementService.getActiveServices();

      if (servicesResponse.success && servicesResponse.data != null) {
        setState(() {
          _availableServices = servicesResponse.data!;
          _isLoadingServices = false;
        });
      } else {
        setState(() {
          _isLoadingServices = false;
        });
      }
    } catch (e) {
      print('Error loading services: $e');
      setState(() {
        _isLoadingServices = false;
      });
    }
  }

  /// Format business name for URL by replacing spaces with underscores and making it URL-safe
  String _formatBusinessNameForUrl(String businessName) {
    return businessName
        .trim()
        .replaceAll(' ', '_')
        .replaceAll(RegExp(r'[^\w\-_]'), '') // Remove special characters except hyphens and underscores
        .toLowerCase();
  }

  /// Update booking website link when business name changes
  void _updateBookingWebsiteLink() {
    final businessName = _businessNameController.text.trim();
    if (businessName.isNotEmpty) {
      final formattedBusinessName = _formatBusinessNameForUrl(businessName);

      // Only update if the slug is empty or follows our auto-generated pattern
      if (_bookingSlugController.text.isEmpty) {
        _bookingSlugController.text = formattedBusinessName;
        _bookingWebsiteController.text = 'https://animalia-programari.ro/book/$formattedBusinessName';
      }
    }
  }

  // Navigation methods
  void _nextStep() {
    if (_currentStep.index < WebsiteManagementStep.values.length - 1) {
      setState(() {
        _currentStep = WebsiteManagementStep.values[_currentStep.index + 1];
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep.index > 0) {
      setState(() {
        _currentStep = WebsiteManagementStep.values[_currentStep.index - 1];
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToStep(WebsiteManagementStep step) {
    setState(() {
      _currentStep = step;
    });
    _pageController.animateToPage(
      step.index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _saveAllSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // Upload photos first if there are any selected
      if (_selectedPhotos.isNotEmpty) {
        print('💾 Uploading ${_selectedPhotos.length} photos before saving...');
        await _uploadPhotos();
      }

      // Get current salon ID
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        throw Exception('No salon ID found');
      }

      print('💾 Current _existingPhotoUrls before save: $_existingPhotoUrls');
      print('💾 Number of photos: ${_existingPhotoUrls.length}');
      print('💾 Online booking enabled: $_onlineBookingEnabled');
      print('💾 Selected service IDs: $_selectedServiceIds');

      // Create web preferences object with all form data including online booking settings
      final webPreferences = SalonWebPreferencesService.createFromFormData(
        salonId: salonId,
        // Send only the slug (not the full URL). Backend expects booking_website_url to be a slug.
        bookingWebsiteUrl: _bookingSlugController.text.trim(),
        businessName: _businessNameController.text.trim(),
        businessDescription: _businessDescriptionController.text.trim(),
        businessAddress: _selectedAddress ?? '',
        contactPhone: _completePhoneNumber.trim(),
        contactEmail: _emailController.text.trim(),
        facebookLink: _facebookController.text.trim(),
        instagramLink: _instagramController.text.trim(),
        tiktokLink: _tiktokController.text.trim(),
        logoUrl: _logoUrl,
        primaryColor: '#${(_selectedPrimaryColor.value & 0xFFFFFF).toRadixString(16).padLeft(6, '0').toUpperCase()}',
        websitePhotos: _existingPhotoUrls,
        businessHours: _businessHours,
        cancellationPolicy: _cancellationPolicy,
        bookingAcceptance: _bookingAcceptance,
        onlineBookingEnabled: _onlineBookingEnabled,
        availableServiceIds: _selectedServiceIds,
        bookingPassword: _bookingPasswordController.text.trim(),
      );

      print('💾 Web preferences object created with ${webPreferences.websitePhotos.length} photos');
      print('💾 Photo URLs in webPreferences: ${webPreferences.websitePhotos}');

      // Save to salon_web_preferences table
      final webPreferencesResponse = await SalonWebPreferencesService.saveWebPreferences(webPreferences);

      print('💾 Save response - Success: ${webPreferencesResponse.success}');
      if (!webPreferencesResponse.success) {
        print('❌ Save error: ${webPreferencesResponse.error}');
      }

      if (webPreferencesResponse.success) {
        // Also update basic salon settings for backward compatibility
        await SettingsService.updateContactInfo(
          website: _bookingWebsiteController.text.trim(),
          phone: _completePhoneNumber.trim(),
          email: _emailController.text.trim(),
          address: _selectedAddress,
        );

        if (mounted) {
          UINotificationService.showSuccess(
            context: context,
            title: context.tr('common.success'),
            message: context.tr('profile_screen.website_updated_success'),
          );

          // Navigate to the summary screen instead of just popping
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const BookingPageSummaryScreen(),
            ),
          );
        }
      } else {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('common.error'),
            message: webPreferencesResponse.error ?? 'Failed to save website preferences',
          );
        }
      }
    } catch (e) {
      print('❌ Save settings error: $e');
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: 'Error saving website preferences: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _openWebsite() async {
    final website = _bookingWebsiteController.text.trim();
    if (website.isEmpty) {
      UINotificationService.showError(
        context: context,
        title: context.tr('common.error'),
        message: context.tr('profile_screen.no_website_to_open'),
      );
      return;
    }

    String url = website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final success = await UrlLauncherService.openWebUrl(url);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'website');
    }
  }

  // Validation methods
  String? _validateWebsite(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Website is optional
    }

    final trimmed = value.trim();

    // Basic URL validation
    final urlPattern = RegExp(
      r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
      caseSensitive: false,
    );

    if (!urlPattern.hasMatch(trimmed)) {
      return context.tr('profile_screen.invalid_website_format');
    }

    return null;
  }

  String? _validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName ${context.tr('common.is_required')}';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Email is optional
    }

    final emailPattern = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailPattern.hasMatch(value.trim())) {
      return context.tr('profile_screen.invalid_email_format');
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return PermissionGuard.management(
      child: Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: AppBar(
          backgroundColor: colorScheme.surface,
          elevation: 0,
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.tr('profile_screen.programming_website_title'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: colorScheme.onSurface,
                ),
              ),
              Text(
                'Configurează pagina de rezervări',
                style: TextStyle(
                  fontSize: 11,
                  color: colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ],
          ),
          actions: [
            if (_bookingWebsiteController.text.trim().isNotEmpty)
              IconButton(
                icon: Icon(Icons.open_in_new, color: colorScheme.primary, size: 20),
                onPressed: _openWebsite,
                tooltip: context.tr('profile_screen.open_website'),
              ),
            IconButton(
              icon: Icon(Icons.help_outline, color: colorScheme.primary, size: 20),
              onPressed: _showHelpDialog,
              tooltip: 'Ajutor',
            ),
          ],
        ),
        body: _isLoading
            ? _buildLoadingView()
            : Column(
                children: [
                  _buildModernStepIndicator(),
                  Expanded(
                    child: Form(
                      key: _formKey,
                      child: PageView(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentStep = WebsiteManagementStep.values[index];
                          });
                        },
                        children: [
                          _buildBasicInfoStep(),
                          _buildCustomizeBookingStep(),
                          _buildBusinessHoursStep(),
                          _buildExtraInfoStep(),
                          _buildBookingAcceptanceStep(),
                        ],
                      ),
                    ),
                  ),
                  _buildNavigationButtons(),
                ],
              ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Card(
            margin: EdgeInsets.only(bottom: 16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: Container(
              height: 100,
              padding: EdgeInsets.all(16),
            ),
          ),
        );
      },
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.help_outline, color: Theme.of(context).primaryColor),
            SizedBox(width: 8),
            Text('Ghid Website Management'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Configurați pagina dvs. de programare online în 5 pași simpli:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              _buildHelpStep('1', 'Informații de bază', 'Setați numele afacerii, descrierea și datele de contact'),
              _buildHelpStep('2', 'Personalizare', 'Adăugați poze pentru a face pagina mai atractivă'),
              _buildHelpStep('3', 'Program', 'Configurați orele de funcționare'),
              _buildHelpStep('4', 'Politici', 'Stabiliți politica de anulare'),
              _buildHelpStep('5', 'Acceptare', 'Alegeți modul de acceptare a programărilor'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Închide'),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpStep(String number, String title, String description) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(14),
            ),
            child: Center(
              child: Text(
                number,
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                ),
                SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernStepIndicator() {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Progress bar
          Row(
            children: WebsiteManagementStep.values.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isActive = step == _currentStep;
              final isCompleted = step.index < _currentStep.index;

              return Expanded(
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 2),
                  height: 6,
                  decoration: BoxDecoration(
                    gradient: isActive || isCompleted
                        ? LinearGradient(
                            colors: [
                              colorScheme.primary,
                              colorScheme.primary.withOpacity(0.7),
                            ],
                          )
                        : null,
                    color: isActive || isCompleted ? null : colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 16),
          // Step indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: WebsiteManagementStep.values.map((step) {
              final isActive = step == _currentStep;
              final isCompleted = step.index < _currentStep.index;

              return Expanded(
                child: GestureDetector(
                  onTap: () => _goToStep(step),
                  child: Column(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          gradient: isActive || isCompleted
                              ? LinearGradient(
                                  colors: [
                                    colorScheme.primary,
                                    colorScheme.primary.withOpacity(0.8),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                )
                              : null,
                          color: isActive || isCompleted ? null : colorScheme.surfaceContainerHighest,
                          shape: BoxShape.circle,
                          boxShadow: isActive
                              ? [
                                  BoxShadow(
                                    color: colorScheme.primary.withOpacity(0.3),
                                    blurRadius: 8,
                                    spreadRadius: 2,
                                  ),
                                ]
                              : null,
                        ),
                        child: Icon(
                          isCompleted ? Icons.check : _getStepIcon(step),
                          color: isActive || isCompleted
                              ? Colors.white
                              : colorScheme.onSurfaceVariant,
                          size: isActive ? 24 : 20,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        _getStepTitle(step),
                        style: TextStyle(
                          color: isActive
                              ? colorScheme.primary
                              : colorScheme.onSurfaceVariant,
                          fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                          fontSize: 11,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  IconData _getStepIcon(WebsiteManagementStep step) {
    switch (step) {
      case WebsiteManagementStep.basicInfo:
        return Icons.info_outline;
      case WebsiteManagementStep.customizeBooking:
        return Icons.palette_outlined;
      case WebsiteManagementStep.businessHours:
        return Icons.schedule_outlined;
      case WebsiteManagementStep.extraInfo:
        return Icons.policy_outlined;
      case WebsiteManagementStep.bookingAcceptance:
        return Icons.verified_outlined;
    }
  }

  Widget _buildStepIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: WebsiteManagementStep.values.map((step) {
          final isActive = step == _currentStep;
          final isCompleted = step.index < _currentStep.index;

          return Expanded(
            child: GestureDetector(
              onTap: () => _goToStep(step),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                child: Column(
                  children: [
                    Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: isActive || isCompleted
                            ? Theme.of(context).primaryColor
                            : Theme.of(context).dividerColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getStepTitle(step),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isActive
                            ? Theme.of(context).primaryColor
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                        fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  String _getStepTitle(WebsiteManagementStep step) {
    switch (step) {
      case WebsiteManagementStep.basicInfo:
        return context.tr('profile_screen.basic_info_step');
      case WebsiteManagementStep.customizeBooking:
        return context.tr('profile_screen.customize_booking_step');
      case WebsiteManagementStep.businessHours:
        return context.tr('profile_screen.business_hours_step');
      case WebsiteManagementStep.extraInfo:
        return context.tr('profile_screen.extra_info_step');
      case WebsiteManagementStep.bookingAcceptance:
        return context.tr('profile_screen.booking_acceptance_step');
    }
  }

  Widget _buildBookingWebsiteField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Flexible(
              child: Text(
                'animalia-programari.ro/book/',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                overflow: TextOverflow.clip,
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: TextFormField(
                controller: _bookingSlugController,
                decoration: InputDecoration(
                  hintText: 'slug',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Required';
                  }
                  return null;
                },
                onChanged: (value) {
                  // Update the full booking website URL when slug changes
                  setState(() {
                    final safeSlug = value.trim().replaceAll(' ', '_');
                    _bookingWebsiteController.text = 'https://animalia-programari.ro/book/$safeSlug';
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBasicInfoStep() {
    final colorScheme = Theme.of(context).colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Booking URL Section - compact
          _buildCompactSection(
            title: 'Link Programare',
            icon: Icons.link,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBookingWebsiteField(),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Business Details Section - compact
          _buildCompactSection(
            title: 'Detalii',
            icon: Icons.store,
            child: Column(
              children: [
                StandardFormField(
                  controller: _businessNameController,
                  labelText: 'Nume',
                  hintText: 'Ex: Beauty Salon',
                  prefixIcon: Icons.business,
                  validator: (value) => _validateRequired(value, 'Nume'),
                ),
                const SizedBox(height: 12),
                StandardFormField(
                  controller: _businessDescriptionController,
                  labelText: 'Descriere',
                  hintText: 'Scurtă descriere...',
                  prefixIcon: Icons.description,
                  maxLines: 2,
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Location Section - compact
          _buildCompactSection(
            title: 'Locație',
            icon: Icons.location_on,
            child: LocationSelectionButton(
              selectedAddress: _selectedAddress,
              hint: 'Selectează adresa',
              onLocationSelected: (location, address) {
                setState(() {
                  _selectedAddress = address;
                });
              },
            ),
          ),
          const SizedBox(height: 12),

          // Contact Information Section - compact
          _buildCompactSection(
            title: 'Contact',
            icon: Icons.contact_mail,
            child: Column(
              children: [
                IntlPhoneField(
                  decoration: InputDecoration(
                    labelText: 'Telefon',
                    hintText: '731 234 567',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  ),
                  initialValue: _completePhoneNumber,
                  initialCountryCode: _initialCountryCode,
                  dropdownIconPosition: IconPosition.trailing,
                  flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                  dropdownTextStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  showDropdownIcon: true,
                  disableLengthCheck: false,
                  keyboardType: TextInputType.phone,
                  onChanged: (phone) {
                    setState(() {
                      _completePhoneNumber = phone.completeNumber;
                    });
                    _saveLastCountryCode(phone.countryISOCode);
                  },
                ),
                const SizedBox(height: 12),
                StandardFormField(
                  controller: _emailController,
                  labelText: 'Email',
                  hintText: '<EMAIL>',
                  prefixIcon: Icons.email,
                  keyboardType: TextInputType.emailAddress,
                  validator: _validateEmail,
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Social Media Section - compact
          _buildCompactSection(
            title: 'Social',
            icon: Icons.share,
            child: Column(
              children: [
                StandardFormField(
                  controller: _facebookController,
                  labelText: 'Facebook',
                  hintText: 'Link Facebook',
                  prefixIcon: Icons.facebook,
                  keyboardType: TextInputType.url,
                ),
                const SizedBox(height: 12),
                StandardFormField(
                  controller: _instagramController,
                  labelText: 'Instagram',
                  hintText: 'Link Instagram',
                  prefixIcon: Icons.camera_alt,
                  keyboardType: TextInputType.url,
                ),
                const SizedBox(height: 12),
                StandardFormField(
                  controller: _tiktokController,
                  labelText: 'TikTok',
                  hintText: 'Link TikTok',
                  prefixIcon: Icons.music_note,
                  keyboardType: TextInputType.url,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildCompactSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: colorScheme.outlineVariant, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(icon, size: 16, color: colorScheme.primary),
                ),
                SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildCustomizeBookingStep() {
    final colorScheme = Theme.of(context).colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Personalizează Pagina de Rezervări',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Alege logo-ul, culoarea și pozele pentru pagina ta de rezervări online',
            style: TextStyle(
              fontSize: 14,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),

          // 1 & 2. Logo and Primary Color side-by-side
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: colorScheme.outlineVariant, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left: Logo block
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: colorScheme.primaryContainer,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Icon(
                                    Icons.image_outlined,
                                    size: 18,
                                    color: colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Logo',
                                        style: TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.bold,
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                      const SizedBox(height: 1),
                                      Text(
                                        'Apare în partea de sus a paginii',
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 14),
                            GestureDetector(
                              onTap: _pickAndUploadLogo,
                              child: Container(
                                width: double.infinity,
                                height: 100,
                                decoration: BoxDecoration(
                                  color: colorScheme.surfaceContainerHighest,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: _logoUrl != null
                                        ? colorScheme.primary.withOpacity(0.4)
                                        : colorScheme.outlineVariant,
                                    width: 1.5,
                                  ),
                                  boxShadow: _logoUrl != null
                                      ? [
                                          BoxShadow(
                                            color: colorScheme.primary.withOpacity(0.08),
                                            blurRadius: 6,
                                            offset: const Offset(0, 2),
                                          ),
                                        ]
                                      : [
                                          BoxShadow(
                                            color: colorScheme.shadow.withOpacity(0.04),
                                            blurRadius: 4,
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                ),
                                child: _logoUrl != null
                                    ? ClipRRect(
                                        borderRadius: BorderRadius.circular(10.5),
                                        child: Image.network(
                                          _logoUrl!,
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                          height: double.infinity,
                                        ),
                                      )
                                    : Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.add_photo_alternate_outlined,
                                            size: 28,
                                            color: colorScheme.primary,
                                          ),
                                          const SizedBox(height: 6),
                                          Text(
                                            'Adaugă Logo',
                                            style: TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w600,
                                              color: colorScheme.primary,
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                            ),
                            if (_isUploadingLogo) ...[
                              const SizedBox(height: 8),
                              Center(
                                child: SizedBox(
                                  width: 18,
                                  height: 18,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Right: Primary color block
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: colorScheme.primaryContainer,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Icon(
                                    Icons.palette_outlined,
                                    size: 18,
                                    color: colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Culoare Primară',
                                        style: TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.bold,
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                      const SizedBox(height: 1),
                                      Text(
                                        'Culoarea principală a paginii tale',
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 14),
                            GestureDetector(
                              onTap: () => _showColorPickerDialog(),
                              child: Container(
                                width: double.infinity,
                                height: 100,
                                decoration: BoxDecoration(
                                  color: _selectedPrimaryColor,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.9),
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: _selectedPrimaryColor.withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 3),
                                    ),
                                    BoxShadow(
                                      color: colorScheme.shadow.withOpacity(0.06),
                                      blurRadius: 4,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.color_lens,
                                        size: 24,
                                        color: _getContrastColor(_selectedPrimaryColor),
                                      ),
                                      const SizedBox(height: 6),
                                      Text(
                                        'Schimbă',
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w600,
                                          color: _getContrastColor(_selectedPrimaryColor),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // 3. Photo upload section - MULTIPLE SELECTION
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: colorScheme.outlineVariant, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.photo_library_outlined,
                          size: 20,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Poze Website',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'Selectează până la 6 poze (poți selecta mai multe deodată)',
                              style: TextStyle(
                                fontSize: 12,
                                color: colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (_isUploadingPhotos)
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPhotoGrid(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get contrasting color for text on colored background
  Color _getContrastColor(Color backgroundColor) {
    // Calculate relative luminance
    final luminance = backgroundColor.computeLuminance();
    // Return white for dark colors, black for light colors
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  Future<void> _pickAndUploadLogo() async {
    try {
      setState(() {
        _isUploadingLogo = true;
      });

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        // Upload immediately to Cloudinary
        final uploadResponse = await ApiService.uploadXFile<ImageUploadResponse>(
          '/api/images/upload',
          image,
          fromJson: (data) => ImageUploadResponse.fromJson(Map<String, dynamic>.from(data)),
        );

        if (uploadResponse.success && uploadResponse.data != null && mounted) {
          setState(() {
            _logoUrl = uploadResponse.data!.url;
            _isUploadingLogo = false;
          });

          UINotificationService.showSuccess(
            context: context,
            title: 'Success',
            message: 'Logo încărcat',
          );
        } else {
          setState(() {
            _isUploadingLogo = false;
          });

          if (mounted) {
            UINotificationService.showError(
              context: context,
              title: 'Eroare',
              message: uploadResponse.error ?? 'Eroare la încărcare',
            );
          }
        }
      } else {
        setState(() {
          _isUploadingLogo = false;
        });
      }
    } catch (e) {
      setState(() {
        _isUploadingLogo = false;
      });

      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare',
          message: 'Eroare la selectare: $e',
        );
      }
    }
  }

  void _showColorPickerDialog() async {
    Color selectedColor = _selectedPrimaryColor;

    // Define colors that work well with white background (darker, saturated colors)
    final Map<ColorSwatch<Object>, String> whiteFriendlyColors = {
      ColorTools.createPrimarySwatch(const Color(0xFF1976D2)): 'Blue',
      ColorTools.createPrimarySwatch(const Color(0xFF0288D1)): 'Light Blue',
      ColorTools.createPrimarySwatch(const Color(0xFF00796B)): 'Teal',
      ColorTools.createPrimarySwatch(const Color(0xFF388E3C)): 'Green',
      ColorTools.createPrimarySwatch(const Color(0xFF689F38)): 'Light Green',
      ColorTools.createPrimarySwatch(const Color(0xFFF57C00)): 'Orange',
      ColorTools.createPrimarySwatch(const Color(0xFFE64A19)): 'Deep Orange',
      ColorTools.createPrimarySwatch(const Color(0xFFD32F2F)): 'Red',
      ColorTools.createPrimarySwatch(const Color(0xFFC2185B)): 'Pink',
      ColorTools.createPrimarySwatch(const Color(0xFF7B1FA2)): 'Purple',
      ColorTools.createPrimarySwatch(const Color(0xFF512DA8)): 'Deep Purple',
      ColorTools.createPrimarySwatch(const Color(0xFF303F9F)): 'Indigo',
      ColorTools.createPrimarySwatch(const Color(0xFF5D4037)): 'Brown',
      ColorTools.createPrimarySwatch(const Color(0xFF455A64)): 'Blue Grey',
      ColorTools.createPrimarySwatch(const Color(0xFF424242)): 'Grey',
    };

    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Alege o culoare primară'),
          content: SingleChildScrollView(
            child: ColorPicker(
              color: selectedColor,
              onColorChanged: (Color color) {
                selectedColor = color;
              },
              width: 40,
              height: 40,
              borderRadius: 4,
              spacing: 5,
              runSpacing: 5,
              wheelDiameter: 155,
              heading: Text(
                'Selectează culoarea',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              subheading: Text(
                'Selectează nuanța',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              wheelSubheading: Text(
                'Culori optimizate pentru fundal alb',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              showMaterialName: true,
              showColorName: true,
              showColorCode: true,
              copyPasteBehavior: const ColorPickerCopyPasteBehavior(
                longPressMenu: true,
              ),
              materialNameTextStyle: Theme.of(context).textTheme.bodySmall,
              colorNameTextStyle: Theme.of(context).textTheme.bodySmall,
              colorCodeTextStyle: Theme.of(context).textTheme.bodySmall,
              // Only show primary colors and custom colors, disable wheel and accent
              pickersEnabled: const <ColorPickerType, bool>{
                ColorPickerType.both: false,
                ColorPickerType.primary: true,
                ColorPickerType.accent: false,
                ColorPickerType.bw: false,
                ColorPickerType.custom: false,
                ColorPickerType.wheel: false,
              },
              // Use custom color swatches that work well with white
              customColorSwatchesAndNames: whiteFriendlyColors,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Anulează'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('Aplică'),
            ),
          ],
        );
      },
    );

    if (result == true) {
      setState(() {
        _selectedPrimaryColor = selectedColor;
      });
    }
  }

  Widget _buildBusinessHoursStep() {
    final colorScheme = Theme.of(context).colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Advanced schedule step widget
          AdvancedScheduleStep(
            initialBusinessHours: _businessHours,
            onChanged: (newBusinessHours) {
              setState(() {
                _businessHours = newBusinessHours;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildExtraInfoStep() {
    final colorScheme = Theme.of(context).colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...CancellationPolicy.values.map((policy) {
            final isSelected = policy == _cancellationPolicy;
            return Container(
              margin: const EdgeInsets.only(bottom: 10),
              decoration: BoxDecoration(
                border: Border.all(
                  color: isSelected ? colorScheme.primary : colorScheme.outlineVariant,
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(10),
                color: isSelected
                    ? colorScheme.primaryContainer.withOpacity(0.3)
                    : colorScheme.surface,
              ),
              child: RadioListTile<CancellationPolicy>(
                title: Text(
                  _getCancellationPolicyText(policy),
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 13,
                  ),
                ),
                value: policy,
                groupValue: _cancellationPolicy,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _cancellationPolicy = value;
                    });
                  }
                },
                activeColor: colorScheme.primary,
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildBookingAcceptanceStep() {
    final colorScheme = Theme.of(context).colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Online Booking Toggle Section
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(
                color: _onlineBookingEnabled ? colorScheme.primary : colorScheme.outlineVariant,
                width: _onlineBookingEnabled ? 2 : 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _onlineBookingEnabled ? Icons.check_circle : Icons.block,
                        color: _onlineBookingEnabled ? colorScheme.primary : colorScheme.onSurfaceVariant,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Programare Online',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _onlineBookingEnabled
                                  ? 'Clienții pot face programări online'
                                  : 'Programările online sunt dezactivate',
                              style: TextStyle(
                                fontSize: 13,
                                color: colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Switch(
                        value: _onlineBookingEnabled,
                        onChanged: (value) {
                          setState(() {
                            _onlineBookingEnabled = value;
                            if (!value) {
                              // Clear selected services when disabling
                              _selectedServiceIds.clear();
                            }
                          });
                        },
                      ),
                    ],
                  ),
                  if (!_onlineBookingEnabled) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: colorScheme.errorContainer.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.warning_amber, color: colorScheme.error, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Pagina de programare va returna eroare 404',
                              style: TextStyle(
                                fontSize: 12,
                                color: colorScheme.error,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Service Selection Section
          if (_onlineBookingEnabled) ...[
            Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: colorScheme.outlineVariant, width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.list_alt, color: colorScheme.tertiary, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Servicii Disponibile Online',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Selectează serviciile care pot fi rezervate online. Dacă nu selectezi niciunul, toate serviciile active vor fi disponibile.',
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 16),

                    if (_isLoadingServices)
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    else if (_availableServices.isEmpty)
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.info_outline, color: colorScheme.onSurfaceVariant),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Nu ai servicii active. Adaugă servicii pentru a le face disponibile online.',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    else ...[
                      // Select All / Deselect All
                      Row(
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              setState(() {
                                if (_selectedServiceIds.length == _availableServices.length) {
                                  _selectedServiceIds.clear();
                                } else {
                                  _selectedServiceIds = _availableServices.map((s) => s.id).toList();
                                }
                              });
                            },
                            icon: Icon(
                              _selectedServiceIds.length == _availableServices.length
                                  ? Icons.check_box
                                  : Icons.check_box_outline_blank,
                              size: 20,
                            ),
                            label: Text(
                              _selectedServiceIds.length == _availableServices.length
                                  ? 'Deselectează Tot'
                                  : 'Selectează Tot',
                              style: TextStyle(fontSize: 12),
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${_selectedServiceIds.length} / ${_availableServices.length}',
                            style: TextStyle(
                              fontSize: 12,
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                      const Divider(),

                      // Service List
                      ..._availableServices.map((service) {
                        final isSelected = _selectedServiceIds.contains(service.id);
                        return CheckboxListTile(
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              if (value == true) {
                                _selectedServiceIds.add(service.id);
                              } else {
                                _selectedServiceIds.remove(service.id);
                              }
                            });
                          },
                          title: Text(
                            service.name,
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            ),
                          ),
                          subtitle: service.description.isNotEmpty
                              ? Text(
                                  service.description,
                                  style: TextStyle(fontSize: 11),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                )
                              : null,
                          secondary: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? colorScheme.primaryContainer
                                  : colorScheme.surfaceContainerHighest,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${service.price.toStringAsFixed(0)} RON',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                                color: isSelected
                                    ? colorScheme.primary
                                    : colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                          controlAffinity: ListTileControlAffinity.leading,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                          dense: true,
                        );
                      }).toList(),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Booking Acceptance Section
          Text(
            'Mod Acceptare Programări',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),

          ...BookingAcceptance.values.map((acceptance) {
            final isSelected = acceptance == _bookingAcceptance;
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                border: Border.all(
                  color: isSelected ? colorScheme.primary : colorScheme.outlineVariant,
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(10),
                color: isSelected
                    ? colorScheme.primaryContainer.withOpacity(0.3)
                    : colorScheme.surface,
              ),
              child: RadioListTile<BookingAcceptance>(
                title: Text(
                  _getBookingAcceptanceText(acceptance),
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
                subtitle: Padding(
                  padding: EdgeInsets.only(top: 6),
                  child: Text(
                    _getBookingAcceptanceDescription(acceptance),
                    style: TextStyle(
                      fontSize: 11,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                value: acceptance,
                groupValue: _bookingAcceptance,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _bookingAcceptance = value;
                    });
                  }
                },
                activeColor: colorScheme.primary,
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            );
          }),

          const SizedBox(height: 24),

          // Booking Password Section - Moved from Customize Step
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: colorScheme.outlineVariant, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.lock_outline,
                          size: 20,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Parolă Programări Online',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'Opțional - Protejează accesul la pagina de rezervări',
                              style: TextStyle(
                                fontSize: 12,
                                color: colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colorScheme.outlineVariant,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 18,
                          color: colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Clienții vor trebui să introducă această parolă pentru a accesa pagina de programări',
                            style: TextStyle(
                              fontSize: 12,
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  StandardFormField(
                    controller: _bookingPasswordController,
                    labelText: 'Parolă (opțional)',
                    hintText: 'Lasă gol pentru acces liber',
                    prefixIcon: Icons.password,
                    obscureText: true,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _uploadPhotos() async {
    if (_selectedPhotos.isEmpty) return;

    setState(() {
      _isUploadingPhotos = true;
    });

    try {
      for (final photo in _selectedPhotos) {
        final uploadResponse = await ApiService.uploadXFile<ImageUploadResponse>(
          '/api/images/upload',
          photo,
          fromJson: (data) => ImageUploadResponse.fromJson(Map<String, dynamic>.from(data)),
        );

        if (uploadResponse.success && uploadResponse.data != null) {
          _existingPhotoUrls.add(uploadResponse.data!.url);
        }
      }

      _selectedPhotos.clear();
      _selectedPhotoPreviews.clear();
      setState(() {});

      if (mounted) {
        UINotificationService.showSuccess(
          context: context,
          title: 'Success',
          message: 'Photos uploaded successfully',
        );
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Error',
          message: 'Error uploading photos: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploadingPhotos = false;
        });
      }
    }
  }

  Widget _buildNavigationButtons() {
    final colorScheme = Theme.of(context).colorScheme;
    final isFirstStep = _currentStep == WebsiteManagementStep.values.first;
    final isLastStep = _currentStep == WebsiteManagementStep.values.last;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (!isFirstStep)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.arrow_back, size: 18),
                    SizedBox(width: 8),
                    Text('Înapoi'),
                  ],
                ),
              ),
            ),
          if (!isFirstStep) SizedBox(width: 12),
          Expanded(
            flex: isFirstStep ? 1 : 1,
            child: ElevatedButton(
              onPressed: _isSaving
                  ? null
                  : (isLastStep ? _saveAllSettings : _nextStep),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: colorScheme.primary,
                foregroundColor: Colors.white,
              ),
              child: _isSaving
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(isLastStep ? 'Salvează' : 'Continuă'),
                        SizedBox(width: 8),
                        Icon(
                          isLastStep ? Icons.check : Icons.arrow_forward,
                          size: 18,
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoGrid() {
    final colorScheme = Theme.of(context).colorScheme;
    final existingCount = _existingPhotoUrls.length;
    final selectedCount = _selectedPhotoPreviews.length;
    final totalCount = existingCount + selectedCount;
    final canAddMore = totalCount < 6;

    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: totalCount + (canAddMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == totalCount) {
          // Add photo button
          return GestureDetector(
            onTap: _pickPhotos,
            child: Container(
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                  width: 1,
                  style: BorderStyle.solid,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.add_photo_alternate, color: colorScheme.primary, size: 32),
                  SizedBox(height: 4),
                  Text(
                    'Adaugă',
                    style: TextStyle(
                      fontSize: 11,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final isExisting = index < existingCount;
        final imageProvider = isExisting
            ? NetworkImage(_existingPhotoUrls[index]) as ImageProvider
            : _selectedPhotoPreviews[index - existingCount];

        return Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image: DecorationImage(
                  image: imageProvider,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              top: 4,
              right: 4,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    if (isExisting) {
                      _existingPhotoUrls.removeAt(index);
                    } else {
                      final selectedIndex = index - existingCount;
                      _selectedPhotos.removeAt(selectedIndex);
                      _selectedPhotoPreviews.removeAt(selectedIndex);
                    }
                  });
                },
                child: Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.close, color: Colors.white, size: 16),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _pickPhotos() async {
    final maxPhotos = 6 - _existingPhotoUrls.length - _selectedPhotos.length;
    if (maxPhotos <= 0) {
      UINotificationService.showError(
        context: context,
        title: 'Limită atinsă',
        message: 'Poți adăuga maxim 6 poze',
      );
      return;
    }

    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        final photosToAdd = images.take(maxPhotos).toList();
        // Build preview image providers for each selected photo.
        for (final photo in photosToAdd) {
          ImageProvider preview;
          if (kIsWeb || photo.path.isEmpty) {
            // On web or when path isn't available, read bytes and use MemoryImage
            final bytes = await photo.readAsBytes();
            preview = MemoryImage(bytes);
          } else {
            // On native platforms, use FileImage for faster loading
            preview = FileImage(File(photo.path));
          }
          _selectedPhotos.add(photo);
          _selectedPhotoPreviews.add(preview);
        }
        setState(() {});
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare',
          message: 'Eroare la selectarea pozelor: $e',
        );
      }
    }
  }

  String _getCancellationPolicyText(CancellationPolicy policy) {
    switch (policy) {
      case CancellationPolicy.NO_CHANGES:
        return 'Fără anulare - Clientul nu poate anula';
      case CancellationPolicy.HOURS_24:
        return 'Anulare cu 24 ore înainte';
      case CancellationPolicy.HOURS_48:
        return 'Anulare cu 48 ore înainte';
      case CancellationPolicy.HOURS_72:
        return 'Anulare cu 72 ore înainte';
    }
  }

  String _getBookingAcceptanceText(BookingAcceptance acceptance) {
    switch (acceptance) {
      case BookingAcceptance.automatic:
        return 'Acceptare Automată';
      case BookingAcceptance.manual:
        return 'Acceptare Manuală';
    }
  }
  String _getBookingAcceptanceDescription(BookingAcceptance acceptance) {
    switch (acceptance) {
      case BookingAcceptance.automatic:
        return 'Programarile sunt acceptate automat si apar in calendar';
      case BookingAcceptance.manual:
        return 'Programarile necesita aprobat manual inainte de confirmare';
    }
  }
}
